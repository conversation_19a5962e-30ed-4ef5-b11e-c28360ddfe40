你是一个测试专家，擅长使用httprunner写集测测试用例，请帮我设计测试用例：
1. 提取所有请求参数中的枚举参数，包括嵌套参数，提取范围包括：
   - 所有请求参数（不包括响应参数）
   - 所有嵌套层级的参数
   - 所有明确列出可选值、取值范围、默认值、单位、格式要求、业务状态等的参数
   - 只允许提取接口文档中明文列出的参数，不允许根据命名规律、推测、上下文联想等方式补充参数
   - 确保提取所有类型的枚举参数，包括整数类型和字符串类型的枚举
   - 仅提取那些在接口文档中明确列出可选值的参数，未列出具体可选值的参数不予提取，不允许臆想、假设、推理、猜测可选值

2. 基于每一个枚举参数，自动生成接口测试用例，要求如下：
   - 每个枚举参数都需覆盖所有可选值的正向用例（每个可选值各生成一个用例）
   - 每个枚举参数都需覆盖非法值的反向用例（如：超出范围、类型错误、格式不符等）
   - 用例只需校验枚举参数的取值正确性，不涉及必填/非必填/缺失场景
   - 其他参数可用任意合法值填充，保持用例最小化

3. 用例格式参考：
   参考用例格式.yml

4. 测试数据规范：
   - 枚举值使用实际可用的值
   - 非法值用明显错误的数据（如：不存在的枚举、类型错误、格式错误等）
   - 错误提示统一使用"参数错误: "作为前缀

5. 特别注意：
   - 只输出最终的测试用例，不输出枚举参数本身，不输出任何解释说明。
   - 写用例时的入参尽量以变量variables的形式传
6. 请严格按照以下要求执行，不要推测任何参数或值：
		1. 只提取接口文档中明确列出的参数。
		2. 不允许根据上下文或命名规律进行推测。
		3. 确保所有输出仅基于提供的信息。