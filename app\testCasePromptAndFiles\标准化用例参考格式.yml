- config:
    name: 3.0主流程场景
    base_url: ${ENV(openapi_url)}
    variables:
      - app_Id: ${ENV(app_Id)}
      - app_secret: ${ENV(app_Id_secret_saas_realname_willing)}
      - file_id: ${ENV(file_id_in_app_Id_saas_realname_willing)}
      - psn_id: ${ENV(psn_1)}
      - psn_seal: ${ENV(psn_1_seal)}


- test:
    name: 3.0轩辕#rsa#手绘+企业+手动+部分审批#可信签
    variables:
      - json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"signFlowTitle":"3.0主流程场景"},"signers":[{"psnSignerInfo":{"psnId":"${psn_id}"}}]}
    api: api/sign/signflows/create-by-file.yml
    extract:
      - signFlowId: content.data.signFlowId
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]

- test:
    name: 查询签署区信息
    variables:
      - flowId: $signFlowId
    api: api/sign/signfields/signfieldsSelect_2.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code", 0]
      - eq: ["content.message", "成功"]
    extract:
      - flowId_signfieldId_1: content.data.signfields.0.signfieldId


#意愿认证
- test:
    name: 意愿认证-发送验证码
    api: api/realname/realname/sendSms_2.yml
    variables:
      - accountId: $psn_id
      - flowId: $signFlowId
    extract:
      - bizId: content.data.bizId
    validate:
      - len_gt: ["content.data.bizId",0]

- test:
    name: 意愿认证-校验验证码
    variables:
      - authcode: 123456
      - accountId: $psn_id
      - bizId: $bizId
    api: api/realname/realname/checkSms.yml
    validate:
      - eq: ["status_code", 200]
      - eq: ["content.code",0]

