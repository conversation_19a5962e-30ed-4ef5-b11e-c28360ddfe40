<p align="center">
  <img src="https://img.shields.io/badge/FastAPI-MCP-green?logo=fastapi" alt="FastAPI MCP" height="28">
</p>

<p align="center">
  <img src="https://user-images.githubusercontent.com/674621/209430382-1e2e1e7e-2e2e-4e7e-8e2e-1e2e1e7e2e2e.png" alt="esign-qa-mcp-service" width="320">
</p>

<h1 align="center">esign-qa-mcp-service</h1>

<p align="center">
  <b>基于 FastAPI + MCP 协议的电子签名自动化测试服务</b><br>
  <sub>证书管理、测试账号、集测提示词、用例自动生成，一站式解决方案</sub>
</p>

---

## ✨ 项目简介

`esign-qa-mcp-service` 是一个专为电子签名自动化测试场景设计的服务，集成了证书管理、测试账号生成、集测提示词管理等自动化能力，支持与 AI 工具（如 lingma）集成，自动生成 HttpRunner 测试用例。

---

## 🚀 功能特性

- <b>MCP 服务自动暴露</b>：通过 fastapi_mcp 自动暴露所有业务工具接口
- <b>证书管理自动化</b>：支持测试账号获取、证书创建、查询、吊销、更新等全流程
- <b>集测提示词管理</b>：支持多种测试用例/接口提示词的获取与搜索
- <b>标准化测试用例生成</b>：内置标准用例和接口格式模板，便于自动化生成
- <b>灵活配置</b>：所有提示词类型和参考文件均通过配置驱动，易于扩展

---

## 🏁 快速开始

<img src="https://img.shields.io/badge/step-1-blue" height="20"> 安装依赖

```bash
pip install -r requirements.txt
```

<img src="https://img.shields.io/badge/step-2-blue" height="20"> 启动服务

```bash
python main.py
```

<img src="https://img.shields.io/badge/step-3-blue" height="20"> 访问服务

- API 文档: http://localhost:8000/docs
- MCP 端点: http://localhost:8000/mcp
- 健康检查: http://localhost:8000/health_check

---

## 📂 主要目录结构

```text
esign-qa-mcp-service/
├── app/
│   ├── core/                  # 配置
│   ├── mcpController/         # 业务路由
│   └── testCasePromptAndFiles # 提示词与用例模板
├── mcpService/                # 业务工具与提示词服务实现
├── main.py                    # 启动入口
├── requirements.txt           # 依赖
├── Dockerfile                 # 容器部署
└── README.md
```

---

## 🧩 常用接口说明

| 接口路径           | 说明                 |
|--------------------|----------------------|
| `/获取测试账号`     | 获取测试账号信息     |
| `/创建证书`         | 创建数字证书         |
| `/查询证书详情`     | 查询证书详细信息     |
| `/吊销证书`         | 吊销数字证书         |
| `/更新证书`         | 更新数字证书信息     |
| `/获取提示词类型`   | 获取所有可用提示词类型 |
| `/获取提示词内容`   | 获取指定类型提示词内容 |
| `/搜索提示词`       | 根据关键词搜索提示词 |
| `/获取全套集测提示词` | 获取所有集测提示词内容 |

---

## 🛠️ 扩展与自定义

- 新增提示词类型：在 `app/testCasePromptAndFiles/prompt_config.yml` 配置新类型，并添加对应文件
- 新增业务工具：在 `mcpService/utils.py` 或 `mcpService/promptService.py` 添加函数，并在 `app/mcpController/businessRoutes.py` 注册路由

---

## 🎯 适用场景

- 电子签名自动化测试
- 测试账号与证书流程自动化
- 测试用例与接口定义标准化生成
- AI 工具集成的自动化测试场景

---

## 📄 许可证

MIT License

---

## 📝 数据库字段类型不匹配故障案例分析

### 问题描述

- 子表 `doc_user` 的 `doc_info_id` 字段类型为 `int`，主表 `doc_info` 的主键为 `bigint`，两者类型不匹配，且主表主键已超出 `int` 最大值，导致数据溢出。

### 故障归类

- 资源-资源异常-数据库字段溢出

### 影响分析

- 业务数据无法正常关联，部分数据插入/查询失败，影响业务连续性。

### 发现与应急问题

1. 字段长度问题未提前监测到（新建未审核、摸排未覆盖）。
2. 监控告警未及时通知相关团队。
3. 应急方案未第一时间评估最优路径（如索引、加索引耗时、影响面评估）。
4. 业务未隔离，影响范围扩大。
5. 故障同步未及时、未评估预计恢复时间。
6. 故障情况未同步给相关负责人（如AR）。

### 预防与改进建议

- 设计阶段严格审核主外键类型一致性，避免类型不匹配。
- 新建表结构需走审核流程，重点关注主外键类型。
- 定期自动化脚本扫描主外键类型一致性。
- 监控数据库主键增长，超阈值提前告警。
- 故障应急流程标准化，定期同步进展和预计恢复时间。
- 业务隔离，降低单点故障影响面。
- 故障同步机制完善，确保所有相关方及时知情。

### 识别与预防共同点

- 主外键类型不一致是常见隐患，需纳入结构审核和自动化检测。
- 监控和告警需覆盖字段溢出、主键增长等风险点。
- 应急流程需标准化，确保信息同步和快速决策。

---

## 🛡️ 数据库字段类型不匹配的落地措施与类似稳定性问题

### 具体落地措施

1. **结构设计阶段**
   - 统一主表与子表主外键字段类型，强制类型校验。
   - 设计评审时增加字段类型一致性检查项。
2. **开发与上线流程**
   - 新建或变更表结构需走DBA审核，重点关注主外键类型。
   - 上线前引入自动化脚本校验主外键类型一致性。
3. **自动化检测与监控**
   - 定期运行SQL脚本，扫描主外键类型不一致的表。
   - 监控主键自增值，接近int/bigint上限时自动告警。
4. **应急与回溯**
   - 建立标准应急流程，快速定位字段溢出问题。
   - 故障发生后，第一时间同步影响面和预计恢复时间。
5. **知识沉淀与培训**
   - 典型案例纳入团队知识库，定期培训开发与DBA。

### 类似的稳定性问题

- 主外键类型不一致导致数据溢出或关联失败
- 字段长度不足（如varchar过短）导致数据截断
- 字段精度不够（如decimal精度丢失）导致金额错误
- 表结构变更未同步到所有环境，导致数据不一致
- 数据库索引缺失或失效，导致性能瓶颈
- 数据库连接数、磁盘空间等资源耗尽

### 解决方向

- **结构规范**：制定并执行数据库设计规范，主外键、长度、精度等有明确标准。
- **自动化检测**：上线前和定期自动检测表结构一致性、字段溢出风险。
- **监控告警**：完善数据库监控，字段溢出、主键增长、索引失效等都能及时告警。
- **流程保障**：上线、变更、应急流程标准化，确保每个环节有检查和回溯。
- **知识沉淀**：故障案例沉淀，形成团队知识库，持续培训和复盘。
