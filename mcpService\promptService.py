#!/usr/bin/env python3
"""
集测提示词服务
"""
import os
import yaml
import logging
from typing import Dict, Any, List, Optional


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PromptService:
    """集测提示词服务"""
    
    def __init__(self):
        self.prompt_config_path = "app/testCasePromptAndFiles/prompt_config.yml"
        self.prompt_dir = "app/testCasePromptAndFiles"
        self.prompt_types = {}
        self._load_prompt_config()
    
    def _load_prompt_config(self):
        """加载提示词配置"""
        try:
            if os.path.exists(self.prompt_config_path):
                with open(self.prompt_config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    self.prompt_types = config.get('prompt_types', {})
                    logger.info(f"成功从配置文件加载 {len(self.prompt_types)} 个提示词类型")
            else:
                logger.warning(f"提示词配置文件不存在: {self.prompt_config_path}")
        except Exception as e:
            logger.error(f"加载提示词配置失败: {str(e)}")
    
    def get_prompt_types(self) -> Dict[str, Any]:
        """获取所有提示词类型"""
        return {
            "status": "success",
            "data": {
                "提示词类型": list(self.prompt_types.keys()),
                "总数": len(self.prompt_types)
            },
            "message": f"获取到 {len(self.prompt_types)} 个提示词类型"
        }
    
    def get_prompt_content(self, prompt_type: str) -> Dict[str, Any]:
        """获取指定类型的提示词内容和参考格式"""
        try:
            if prompt_type not in self.prompt_types:
                return {
                    "status": "error",
                    "message": f"未找到提示词类型: {prompt_type}"
                }

            prompt_info = self.prompt_types[prompt_type]

            # 读取提示词文件
            prompt_file_path = os.path.join(self.prompt_dir, prompt_info['file'])
            if not os.path.exists(prompt_file_path):
                return {
                    "status": "error",
                    "message": f"提示词文件不存在: {prompt_file_path}"
                }

            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                prompt_content = f.read()

            # 读取参考格式文件
            reference_content = None
            reference_file_path = None
            if 'reference_file' in prompt_info:
                reference_file_path = os.path.join(self.prompt_dir, prompt_info['reference_file'])
                if os.path.exists(reference_file_path):
                    with open(reference_file_path, 'r', encoding='utf-8') as f:
                        reference_content = f.read()
                else:
                    logger.warning(f"参考格式文件不存在: {reference_file_path}")

            result_data = {
                "提示词类型": prompt_type,
                "描述": prompt_info.get('description', ''),
                "提示词内容": prompt_content,
                "提示词文件路径": prompt_file_path
            }

            # 如果有参考格式，添加到结果中
            if reference_content:
                result_data.update({
                    "参考格式内容": reference_content,
                    "参考格式文件路径": reference_file_path,
                    "参考格式文件名": prompt_info['reference_file']
                })

            return {
                "status": "success",
                "data": result_data,
                "message": f"成功获取提示词和参考格式: {prompt_type}"
            }

        except Exception as e:
            logger.error(f"获取提示词内容失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取提示词内容失败: {str(e)}"
            }
    
    def get_all_prompts(self) -> Dict[str, Any]:
        """获取所有提示词内容"""
        try:
            all_prompts = {}
            
            for prompt_type in self.prompt_types.keys():
                result = self.get_prompt_content(prompt_type)
                if result.get("status") == "success":
                    all_prompts[prompt_type] = result["data"]
            
            return {
                "status": "success",
                "data": {
                    "提示词总数": len(all_prompts),
                    "提示词内容": all_prompts
                },
                "message": f"成功获取 {len(all_prompts)} 个提示词"
            }
            
        except Exception as e:
            logger.error(f"获取所有提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"获取所有提示词失败: {str(e)}"
            }
    
    def search_prompts(self, keyword: str) -> Dict[str, Any]:
        """搜索提示词"""
        try:
            matching_prompts = {}
            
            for prompt_type, prompt_info in self.prompt_types.items():
                # 在类型名称和描述中搜索
                if (keyword.lower() in prompt_type.lower() or 
                    keyword.lower() in prompt_info.get('description', '').lower()):
                    
                    result = self.get_prompt_content(prompt_type)
                    if result.get("status") == "success":
                        matching_prompts[prompt_type] = result["data"]
            
            return {
                "status": "success",
                "data": {
                    "搜索关键词": keyword,
                    "匹配数量": len(matching_prompts),
                    "匹配结果": matching_prompts
                },
                "message": f"找到 {len(matching_prompts)} 个匹配的提示词"
            }
            
        except Exception as e:
            logger.error(f"搜索提示词失败: {str(e)}")
            return {
                "status": "error",
                "message": f"搜索提示词失败: {str(e)}"
            }


# 全局实例
prompt_service = PromptService()


def get_prompt_types() -> Dict[str, Any]:
    """获取所有提示词类型"""
    return prompt_service.get_prompt_types()


def get_prompt_content(prompt_type: str) -> Dict[str, Any]:
    """获取指定类型的提示词内容"""
    return prompt_service.get_prompt_content(prompt_type)


def get_all_prompts() -> Dict[str, Any]:
    """获取所有提示词内容"""
    return prompt_service.get_all_prompts()


def search_prompts(keyword: str) -> Dict[str, Any]:
    """搜索提示词"""
    return prompt_service.search_prompts(keyword)


def get_接口生成提示词() -> Dict[str, Any]:
    """获取接口生成提示词"""
    return get_prompt_content("接口生成提示词")


def get_必填参数提示词() -> Dict[str, Any]:
    """获取必填参数提示词"""
    return get_prompt_content("必填参数提示词")


def get_枚举值提示词() -> Dict[str, Any]:
    """获取枚举值提示词"""
    return get_prompt_content("枚举值提示词")


def get_必填与枚举合并提示词() -> Dict[str, Any]:
    """获取必填与枚举合并提示词"""
    return get_prompt_content("必填与枚举合并提示词")


def get_通用HttpRunner测试用例生成提示词() -> Dict[str, Any]:
    """获取通用HttpRunner测试用例生成提示词"""
    return get_prompt_content("通用HttpRunner测试用例生成提示词")


def get_智能HttpRunner生成提示词() -> Dict[str, Any]:
    """获取智能HttpRunner生成提示词"""
    return get_prompt_content("智能HttpRunner生成提示词")


def get_全套集测提示词() -> Dict[str, Any]:
    """获取全套集测提示词"""
    return get_all_prompts()
