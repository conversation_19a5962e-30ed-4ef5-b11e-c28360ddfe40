{'url': 'http://sdk.testk8s.tsign.cn/random/get', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {}, 'status_code': 200, 'output': '{"success":true,"accountList":[{"orgCode":"910000005K70DF6A4Q","idNo":"******************","name":"测试屠思","englishName":"<PERSON>","bankCard":"****************","phone":"***********","orgName":"esigntest屠思经营的个体工商户"}]}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certParam': {'algorithm': 'SM2', 'certPolicy': 'COMMON', 'certTime': 'ONEYEAR', 'certType': 'SINGLE', 'configId': '**********', 'isUkey': False, 'issuer': 'ZHCA'}, 'commonParam': {'address': '天堂软件园', 'mobile': '***********', 'phone': '***********'}, 'userParam': {'certName': '测试屠思', 'licenseNumber': '******************', 'licenseType': 19}}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"signCert":"MIIC/DCCAqCgAwIBAgIMOIiKLKbZnwKlKASbMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjExMDYxODE3WhcNMjYwNjExMDYxODE3WjAkMRUwEwYDVQQDDAzmtYvor5XlsaDmgJ0xCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEGDoNyj/eYpD6VPObRFBkyWM3UyqlLdvJC7h3k4JcrHl0ufrgWZuUT3+F/3Z0OZw+qsKVdczvQUs6BFWgNNvCIaOCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTYyLmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNDMwNzgxMTk5ODA2MDk1MjM4MB0GA1UdDgQWBBQTinesic4SqTHI4XYoca9zTDFpCTALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANIADBFAiEAv6uN93hlJIcqgvr0QuXlbRuSc0vFc2ygMLtpti3gun8CIDQkkFPVCCnl6IkO5uNs8T43B2yk2tG/Tbi9/ZK7L9nn","encCert":null,"privateKey":"CI4D4KN0bstCKeDXIPqgKzRLgKXZQiG415mEkKiVmQ==\\n","extention":"{\\"encKey\\":\\"\\",\\"timeUsed\\":17,\\"transactionCode\\":\\"2506111418171645361\\"}","certInfoId":-177926,"algorithm":"SM2"}'}
{'url': 'http://sdk.testk8s.tsign.cn/random/get', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {}, 'status_code': 200, 'output': '{"success":true,"accountList":[{"orgCode":"910000000DJG83A02R","idNo":"654201199806128031","name":"测试毕良","englishName":"Darrell Ralph Torres","bankCard":"6230006224480689865","phone":"***********","orgName":"esigntest毕良经营的个体工商户"}]}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certParam': {'algorithm': 'SM2', 'certPolicy': 'COMMON', 'certTime': 'ONEYEAR', 'certType': 'SINGLE', 'configId': '**********', 'isUkey': False, 'issuer': 'ZHCA'}, 'commonParam': {'address': '天堂软件园', 'mobile': '***********', 'phone': '***********'}, 'userParam': {'certName': '测试毕良', 'licenseNumber': '654201199806128031', 'licenseType': 19}}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"signCert":"MIIC/DCCAqCgAwIBAgIMOIiL138XcScnPMhUMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjExMDYzMTI0WhcNMjYwNjExMDYzMTI0WjAkMRUwEwYDVQQDDAzmtYvor5Xmr5Xoia8xCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE5Jxy96ayNExrEa5hMBCBmSkVNXXwzt0tVJKyoKl9mvyfTOT1GF1OWl5/QwUOiHti7pjITv+/XejDbR2x8dneuaOCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTYyLmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNjU0MjAxMTk5ODA2MTI4MDMxMB0GA1UdDgQWBBQ8wiXY15bhv59v5umbBD/fQLFMoDALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANIADBFAiAoEwUJehICB29m3NfjAA9eBvlSgf8tfOL/A6TVC01ODwIhANdhIHqjMRa598FZodBk1JmFaEMFXef7P0PdFmJPfLp7","encCert":null,"privateKey":"ANnTYNy9eBw+vcyI+mnzk6YBmEB4jxOQsDg+ymjVfEWM\\n","extention":"{\\"encKey\\":\\"\\",\\"timeUsed\\":6,\\"transactionCode\\":\\"2506111431245617109\\"}","certInfoId":-177963,"algorithm":"SM2"}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certInfoId': 'test_cert_id', 'configId': '**********'}, 'status_code': 200, 'output': '{"errCode":1000001,"msg":"HV000116: The object to be validated must not be null.","errShow":true}'}
{'url': 'http://sdk.testk8s.tsign.cn/random/get', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {}, 'status_code': 200, 'output': '{"success":true,"accountList":[{"orgCode":"91000000XW26PYFK4J","idNo":"******************","name":"测试后涛","englishName":"Jay Earl Price","bankCard":"****************","phone":"***********","orgName":"esigntest后涛经营的个体工商户"}]}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certParam': {'algorithm': 'SM2', 'certPolicy': 'COMMON', 'certTime': 'ONEYEAR', 'certType': 'SINGLE', 'configId': '**********', 'isUkey': False, 'issuer': 'ZHCA'}, 'commonParam': {'address': '天堂软件园', 'mobile': '***********', 'phone': '***********'}, 'userParam': {'certName': '测试证书', 'licenseNumber': '******************', 'licenseType': 19}}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"signCert":"MIIC/DCCAqCgAwIBAgIMOIiNs2zsghdzQEETMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjExMDY0NjAyWhcNMjYwNjExMDY0NjAyWjAkMRUwEwYDVQQDDAzmtYvor5Xor4HkuaYxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAELXFccsCNJ7Gmhfv3Ce594zS3ZQsrk5sFbZKxqqupVoEtlcw6fzhoDm4CLcjxynKVMeSOLwJzhqAZHKqNvyyz/qOCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTYyLmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNDExMTAwMTk3ODA4MjQxNzM0MB0GA1UdDgQWBBRExfMvEk+HDva4e8PV5pWvZhxpnzALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANIADBFAiEA4akWXNV0mrWxamL5egf+jdjVk5WB0eaJvWWisP1Zow8CIFto0Nt2gmYTLyrJiQhdiPzKIhBKOeOFne7eK0iT5kvL","encCert":null,"privateKey":"ALxMKYERl0c4PvXO4n8XRBMwyDsLa1RVqlasG1faWXaA\\n","extention":"{\\"encKey\\":\\"\\",\\"timeUsed\\":18,\\"transactionCode\\":\\"2506111446024896757\\"}","certInfoId":-177976,"algorithm":"SM2"}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certInfoId': '123456', 'configId': '**********'}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"certInfo":{"issuer":"WISDOMCA","authcode":null,"certname":"钱二两","serialno":"8efb985cfd3449a6817767948e1b692f2020103","projectid":"**********","licensenumber":"33010219370707191X","id":123456,"isukey":0,"status":1,"certtime":99,"certtype":1,"algorithm":1,"certpolicy":0,"licensetype":19,"createTime":"Oct 29, 2020 11:07:59 AM"}}'}
{'url': 'http://sdk.testk8s.tsign.cn/random/get', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {}, 'status_code': 200, 'output': '{"success":true,"accountList":[{"orgCode":"910000002FCD616313","idNo":"620824194803191065","name":"测试门韵芬","englishName":"Sandra Lucille Harper","bankCard":"****************","phone":"***********","orgName":"esigntest门韵芬经营的个体工商户"}]}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certParam': {'algorithm': 'SM2', 'certPolicy': 'COMMON', 'certTime': 'ONEYEAR', 'certType': 'SINGLE', 'configId': '**********', 'isUkey': False, 'issuer': 'ZHCA'}, 'commonParam': {'address': '天堂软件园', 'mobile': '${步骤1.手机号}', 'phone': '${步骤1.手机号}'}, 'userParam': {'certName': '测试证书', 'licenseNumber': '${步骤1.证件号}', 'licenseType': 19}}, 'status_code': 200, 'output': '{"errCode":1000001,"msg":"[计费制证异常]ZHCA:中环CA请求异常，错误信息为：{\\"ret\\":1008,\\"msg\\":\\"证件号码格式错误\\"}","errShow":true}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certInfoId': '${步骤2.证书id}', 'configId': '**********'}, 'status_code': 200, 'output': '{"errCode":1000001,"msg":"HV000116: The object to be validated must not be null.","errShow":true}'}
{'url': 'http://sdk.testk8s.tsign.cn/random/get', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {}, 'status_code': 200, 'output': '{"success":true,"accountList":[{"orgCode":"9100000081E1R0014Q","idNo":"******************","name":"测试魏瑶馨","englishName":"Samantha Dawn Willis","bankCard":"****************","phone":"***********","orgName":"esigntest魏瑶馨经营的个体工商户"}]}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certParam': {'algorithm': 'SM2', 'certPolicy': 'COMMON', 'certTime': 'ONEYEAR', 'certType': 'SINGLE', 'configId': '**********', 'isUkey': False, 'issuer': 'ZHCA'}, 'commonParam': {'address': '天堂软件园', 'mobile': '***********', 'phone': '***********'}, 'userParam': {'certName': '测试证书', 'licenseNumber': '******************', 'licenseType': 19}}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"signCert":"MIIC/DCCAqCgAwIBAgIMOIiPKyVth0S9vRgJMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjExMDY1NzM1WhcNMjYwNjExMDY1NzM1WjAkMRUwEwYDVQQDDAzmtYvor5Xor4HkuaYxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE97NDO7bPEAkQPj2zUbemmY6ouMiVhpXUeJHeg5jonxsLd9bX/lECZinsHT1loskw5pUGgs0hm7+KwP0V2AcBGaOCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTYyLmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNDEwMTgzMTk2MjExMzA1ODg0MB0GA1UdDgQWBBQqIOpgST/e5q8Q/1+RktUPPXIBnTALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANIADBFAiEAkezo3HMHh43TJwtllNPzJa5q6BCQLHhllhp5GzcMvN0CIEWrEWYoEU6LgPDOVWn0rvhdWdw2vmShIvG/svqUPckg","encCert":null,"privateKey":"Gf+8KJg4ktbcqCZa9/AnE0/CGJoxyhgkp742q18CHUc=\\n","extention":"{\\"encKey\\":\\"\\",\\"timeUsed\\":24,\\"transactionCode\\":\\"2506111457355679742\\"}","certInfoId":-177979,"algorithm":"SM2"}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certInfoId': '-177979', 'configId': '**********'}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"certInfo":{"issuer":"ZHCA","authcode":null,"certname":"测试证书","serialno":"38888f2b256d8744bdbd1809","projectid":null,"licensenumber":"******************","id":-177979,"isukey":0,"status":1,"certtime":1,"certtype":1,"algorithm":2,"certpolicy":0,"licensetype":19,"createTime":"Jun 11, 2025 2:59:30 PM"}}'}
{'url': 'http://sdk.testk8s.tsign.cn/random/get', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {}, 'status_code': 200, 'output': '{"success":true,"accountList":[{"orgCode":"91000000CR2AWU048E","idNo":"******************","name":"测试贡英","englishName":"Jacqueline Marilyn James","bankCard":"****************","phone":"***********","orgName":"esigntest贡英经营的个体工商户"}]}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certParam': {'algorithm': 'SM2', 'certPolicy': 'COMMON', 'certTime': 'ONEYEAR', 'certType': 'SINGLE', 'configId': '**********', 'isUkey': False, 'issuer': 'ZHCA'}, 'commonParam': {'address': '天堂软件园', 'mobile': '***********', 'phone': '***********'}, 'userParam': {'certName': '测试贡英的证书', 'licenseNumber': '******************', 'licenseType': 19}}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"signCert":"MIIDBjCCAqmgAwIBAgIMOIiRgu2DEEWM+tl1MAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjExMDcxNjAxWhcNMjYwNjExMDcxNjAxWjAtMR4wHAYDVQQDDBXmtYvor5XotKHoi7HnmoTor4HkuaYxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE/CCVVshXBCTRUM/enUQqV+iAPLeDkL3jhawxRDG+8qq8Cs7TYJBMap2OYJhkgK0VhySUlDLXE9z5roPKCtk0e6OCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTYyLmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNDEwNzExMTk5MDAxMDQ1NDQxMB0GA1UdDgQWBBQgpTKMZVDTuBQ8xPp23cTilGIBgjALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANJADBGAiEA9O+m1hkH1eR5/HL7LJ5zcdfDSxADgNIUFtxmkk7gaSMCIQDKTMULsy8REihPqtPnyk1YdmO/Z6ycmnX3RZ61yITPKA==","encCert":null,"privateKey":"AKC4uqWxANnCtJQhaVM0H0+jaxSvpaPvW03aPF2H5zif\\n","extention":"{\\"encKey\\":\\"\\",\\"timeUsed\\":18,\\"transactionCode\\":\\"2506111516019712309\\"}","certInfoId":-177986,"algorithm":"SM2"}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certInfoId': '-177986', 'configId': '**********'}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"certInfo":{"issuer":"ZHCA","authcode":null,"certname":"测试贡英的证书","serialno":"38889182ed8310458cfad975","projectid":null,"licensenumber":"******************","id":-177986,"isukey":0,"status":1,"certtime":1,"certtype":1,"algorithm":2,"certpolicy":0,"licensetype":19,"createTime":"Jun 11, 2025 3:17:56 PM"}}'}
{'url': 'http://sdk.testk8s.tsign.cn/random/get', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {}, 'status_code': 200, 'output': '{"success":true,"accountList":[{"orgCode":"910000009A4RJN2E8L","idNo":"533300200603207457","name":"测试左风楠","englishName":"Frederick Robinson","bankCard":"****************","phone":"***********","orgName":"esigntest左风楠经营的个体工商户"}]}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certParam': {'algorithm': 'SM2', 'certPolicy': 'COMMON', 'certTime': 'ONEYEAR', 'certType': 'SINGLE', 'configId': '**********', 'isUkey': False, 'issuer': 'ZHCA'}, 'commonParam': {'address': '天堂软件园', 'mobile': '***********', 'phone': '***********'}, 'userParam': {'certName': '测试左风楠的证书', 'licenseNumber': '533300200603207457', 'licenseType': 19}}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"signCert":"MIIDCTCCAqygAwIBAgIMOIiajvbsE2KAzOoXMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNjExMDgyNzE0WhcNMjYwNjExMDgyNzE0WjAwMSEwHwYDVQQDDBjmtYvor5Xlt6bpo47mpaDnmoTor4HkuaYxCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEMcytkMpN8x8cxBLhTL+rEOCqGFZRv8m4Ocbkx6wMxA2jvP+eboe1H+uYfdzjigOBRZqdoWQJbEqSvvNPJnW6LKOCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTYyLmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNTMzMzAwMjAwNjAzMjA3NDU3MB0GA1UdDgQWBBSg4L4wTDFKu2v/H181zrkEhzUn7zALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANJADBGAiEAs/o1qMNxxOwYyI5cTLRLmeVO71Vnud7vilv8MN9OWzUCIQCIcG37UbEc96gwvGfJ601u9y45sWA1hyvfWQMePWorRA==","encCert":null,"privateKey":"AIEEihzhFhuDgKQmLsU40em6h0JGgmxOQftcmgEy7bxq\\n","extention":"{\\"encKey\\":\\"\\",\\"timeUsed\\":115,\\"transactionCode\\":\\"2506111627142228799\\"}","certInfoId":-178131,"algorithm":"SM2"}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/detail', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certInfoId': '-178131', 'configId': '**********'}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false,"certInfo":{"issuer":"ZHCA","authcode":null,"certname":"测试左风楠的证书","serialno":"38889a8ef6ec136280ccea17","projectid":null,"licensenumber":"533300200603207457","id":-178131,"isukey":0,"status":1,"certtime":1,"certtype":1,"algorithm":2,"certpolicy":0,"licensetype":19,"createTime":"Jun 11, 2025 4:29:09 PM"}}'}
{'url': 'http://cert-service.testk8s.tsign.cn/openca/rest/cert/revoke', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'DEFAULT'}, 'input': {'certInfoId': '-178131'}, 'status_code': 200, 'output': '{"errCode":0,"msg":"成功","errShow":false}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '330103199001010000', 'idType': 'CRED_PSN_CH_IDCARD', 'mobile': '***********', 'name': '测试用户', 'thirdPartyUserId': 'test_user'}, 'status_code': 200, 'output': '{"code":0,"message":"成功","data":{"accountId":"c5ce215e30d74beb9d8936dfcd97d39d"}}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '330103199001010000', 'idType': 'CRED_PSN_CH_IDCARD', 'mobile': '***********', 'name': '测试用户', 'thirdPartyUserId': 'test_user'}, 'status_code': 200, 'output': '{"code":********,"message":"账号已存在","data":{"accountId":"c5ce215e30d74beb9d8936dfcd97d39d"}}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '330103199001010000', 'idType': 'CRED_PSN_CH_IDCARD', 'mobile': '***********', 'name': '测试用户', 'thirdPartyUserId': 'test_user'}, 'status_code': 200, 'output': '{"code":********,"message":"账号已存在","data":{"accountId":"c5ce215e30d74beb9d8936dfcd97d39d"}}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '341502200208185442', 'idType': 'CRED_PSN_CH_IDCARD', 'mobile': '***********', 'name': '测试能勤梦', 'thirdPartyUserId': 'test123'}, 'status_code': 200, 'output': '{"code":0,"message":"成功","data":{"accountId":"7784cd7eb56f4f10b74e6febbcdcddca"}}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/accounts/createByThirdPartyUserId', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '341502200208185442', 'idType': 'CRED_PSN_CH_IDCARD', 'mobile': '***********', 'name': '测试能勤梦', 'thirdPartyUserId': 'test123'}, 'status_code': 200, 'output': '{"code":********,"message":"账号已存在","data":{"accountId":"7784cd7eb56f4f10b74e6febbcdcddca"}}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/organizations', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '9100000094E8NCT9X5', 'idType': 'CRED_ORG_USCC', 'mobile': '***********', 'name': 'esigntest戴清轩经营的个体工商户', 'thirdPartyUserId': 'test_user_456', 'orgLegalIdNumber': '371325197503139638', 'orgLegalName': '测试戴清轩'}, 'status_code': 200, 'output': '{"code":1560200,"message":"open user不存在. ouid:null","data":null}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/organizations/createByThirdPartyUserId', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '9100000094E8NCT9X5', 'idType': 'CRED_ORG_USCC', 'mobile': '***********', 'name': 'esigntest戴清轩经营的个体工商户', 'thirdPartyUserId': 'test_user_456', 'orgLegalIdNumber': '371325197503139638', 'orgLegalName': '测试戴清轩'}, 'status_code': 200, 'output': '{"code":0,"message":"成功","data":{"orgId":"06badf1cad6a4b0f92332b33acffa1b0"}}'}
{'url': 'http://in-test-openapi.tsign.cn/v1/organizations/createByThirdPartyUserId', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': None, 'filter-result': 'false'}, 'input': {'idNumber': '9100000094E8NCT9X5', 'idType': 'CRED_ORG_USCC', 'mobile': '***********', 'name': 'esigntest戴清轩经营的个体工商户', 'thirdPartyUserId': 'test_user_456', 'orgLegalIdNumber': '371325197503139638', 'orgLegalName': '测试戴清轩'}, 'status_code': 200, 'output': '{"code":********,"message":"机构已存在","data":{"accountId":"06badf1cad6a4b0f92332b33acffa1b0","orgId":"06badf1cad6a4b0f92332b33acffa1b0"}}'}
{'url': 'http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'non-standard-v3', 'x-tsign-gray-tag': 'non-standard-v3', 'filter-result': 'false'}, 'input': {'docs': [{'fileId': 'db7dfff6a3a349a58ad217a992bad54e', 'fileName': 'xiugaihou.pdf'}], 'flowInfo': {'autoArchive': True, 'autoInitiate': True, 'businessScene': '签署'}, 'signers': [{'platformSign': False, 'signOrder': 1, 'signerAccount': {'authorizedAccountId': '824c4236a3f242449b93a1c93b10ac9a', 'signerAccountId': '824c4236a3f242449b93a1c93b10ac9a', 'noticeType': ''}, 'signfields': [{'fieldType': 0, 'assignedPosbean': False, 'autoExecute': True, 'actorIndentityType': 0, 'sealId': '', 'fileId': 'db7dfff6a3a349a58ad217a992bad54e', 'posBean': {'posPage': '1', 'posX': 200, 'posY': 200}, 'sealType': '', 'signDateBeanType': '1', 'signDateBean': {'fontSize': 20, 'posX': 150, 'posY': 150}, 'signType': 1, 'width': 150}], 'thirdOrderNo': '11111'}]}, 'status_code': 200, 'output': '{"code":0,"message":"成功","data":{"flowId":"96dada037ba24ae6b29e22b4594c1fb2"}}'}
{'url': 'http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep', 'method': 'POST', 'headers': {'Content-Type': 'application/json', 'X-Tsign-Open-Auth-Mode': 'simple', 'X-Tsign-Open-App-Id': '**********', 'X-Tsign-Service-Group': 'non-standard-v3', 'x-tsign-gray-tag': 'non-standard-v3', 'filter-result': 'false'}, 'input': {'docs': [{'fileId': 'db7dfff6a3a349a58ad217a992bad54e', 'fileName': 'xiugaihou.pdf'}], 'flowInfo': {'autoArchive': True, 'autoInitiate': True, 'businessScene': '签署'}, 'signers': [{'platformSign': False, 'signOrder': 1, 'signerAccount': {'authorizedAccountId': '824c4236a3f242449b93a1c93b10ac9a', 'signerAccountId': '824c4236a3f242449b93a1c93b10ac9a', 'noticeType': ''}, 'signfields': [{'fieldType': 0, 'assignedPosbean': False, 'autoExecute': True, 'actorIndentityType': 0, 'sealId': '', 'fileId': 'db7dfff6a3a349a58ad217a992bad54e', 'posBean': {'posPage': '1', 'posX': 200, 'posY': 200}, 'sealType': '', 'signDateBeanType': '1', 'signDateBean': {'fontSize': 20, 'posX': 150, 'posY': 150}, 'signType': 1, 'width': 150}], 'thirdOrderNo': '11111'}]}, 'status_code': 200, 'output': '{"code":1435002,"message":"参数错误: 用户未授权或授权已过期，不允许静默签署。accountId:824c4236a3f242449b93a1c93b10ac9a","data":null}'}
{
  "url": "http://test.com",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "DEFAULT"
  },
  "input": {
    "test": "data"
  },
  "status_code": 200,
  "output": "{\"code\": 0, \"data\": \"test\"}",
  "timestamp": null
}
================================================================================
{
  "url": "http://test.com",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "DEFAULT"
  },
  "input": {
    "test": "data"
  },
  "status_code": 200,
  "output": "{\"code\": 0, \"data\": \"test\"}",
  "timestamp": null
}
================================================================================
{
  "url": "http://sdk.testk8s.tsign.cn/random/get",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "DEFAULT"
  },
  "input": {},
  "status_code": 200,
  "output": "{\"success\":true,\"accountList\":[{\"orgCode\":\"91000000U7P3FA1D4N\",\"idNo\":\"500108196209285855\",\"name\":\"测试习力振\",\"englishName\":\"Jerry Don Armstrong\",\"bankCard\":\"623074329779026445\",\"phone\":\"***********\",\"orgName\":\"esigntest习力振经营的个体工商户\"}]}",
  "timestamp": null
}
================================================================================
{
  "url": "http://cert-service.testk8s.tsign.cn/openca/rest/cert/createnew",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "DEFAULT"
  },
  "input": {
    "certParam": {
      "algorithm": "SM2",
      "certPolicy": "COMMON",
      "certTime": "ONEYEAR",
      "certType": "SINGLE",
      "configId": "**********",
      "isUkey": false,
      "issuer": "ZHCA"
    },
    "commonParam": {
      "address": "天堂软件园",
      "mobile": "***********",
      "phone": "***********"
    },
    "userParam": {
      "certName": "测试习力振",
      "licenseNumber": "500108196209285855",
      "licenseType": 19
    }
  },
  "status_code": 200,
  "output": "{\"errCode\":0,\"msg\":\"成功\",\"errShow\":false,\"signCert\":\"MIIC/zCCAqOgAwIBAgIMOJkXJHB7bvRMbYqiMAwGCCqBHM9VAYN1BQAwKDEZMBcGA1UEAwwQVEVTVCBaSENBIFNNMiBDQTELMAkGA1UEBhMCQ04wHhcNMjUwNzA0MTAwNTQ4WhcNMjYwNzA0MTAwNTQ4WjAnMRgwFgYDVQQDDA/mtYvor5XkuaDlipvmjK8xCzAJBgNVBAYTAkNOMFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAERhC86u/4dJ0WjqF86DxDRg13Uhm/GN0WuN+6iHjrfQDn8oHsWQso9NBNHbG4/fd2loaYqaBayslaEYJ2ibBjcqOCAbAwggGsMB8GA1UdIwQYMBaAFJR5Q22ZaA/FWsypkn58HUCZKRdaMFcGA1UdIARQME4wTAYEVR0gADBEMEIGCCsGAQUFBwIBFjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjcwgZgGCCsGAQUFBwEBBIGLMIGIMEIGCCsGAQUFBzABhjZodHRwczovL3d3dy50anpoY2EuY29tL0hvbWUvU3VwcG9ydFNlcnZpY2VzP3N0YXRlPXRhYjMwQgYIKwYBBQUHMAKGNmh0dHBzOi8vd3d3LnRqemhjYS5jb20vSG9tZS9TdXBwb3J0U2VydmljZXM/c3RhdGU9dGFiMzA3BgNVHR8EMDAuMCygKqAohiZodHRwOi8veGtleS50anpoY2EuY29tOjEwMDIxLzkvMTg1LmNybDAJBgNVHRMEAjAAMCUGCCqBHNAUBAEBAQH/BBagFBMSNTAwMTA4MTk2MjA5Mjg1ODU1MB0GA1UdDgQWBBQEKRJh8y618GykUuv/RlXmSsuFIzALBgNVHQ8EBAMCBsAwDAYIKoEcz1UBg3UFAANIADBFAiEAvxK5Pyu99VY21iLg2Vh4s+QnnzO2ju6AOHTmmv81i+sCIDJ4FJBphamldsLE9M+FRenHs771hgHOK4KGW15tyIjo\",\"encCert\":null,\"privateKey\":\"agW2KR+k5HVJ3cIuIWq5/JGXDDaMYsCLwsN6nKheirc=\\n\",\"extention\":\"{\\\"encKey\\\":\\\"\\\",\\\"timeUsed\\\":105,\\\"transactionCode\\\":\\\"2507041805486935416\\\"}\",\"certInfoId\":-198024,\"algorithm\":\"SM2\"}",
  "timestamp": null
}
================================================================================
