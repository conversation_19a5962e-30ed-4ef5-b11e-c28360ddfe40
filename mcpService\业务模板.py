#!/usr/bin/env python3
"""
通用工具类
"""
import json

import requests
import logging
from typing import Dict, Any, Optional


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 环境配置
ENV_URLS = {
    "test": "http://sdk.testk8s.tsign.cn",
    "prod": "http://sdk.tsign.cn"
}

CERT_ENV_URLS = {
    "test": "http://cert-service.testk8s.tsign.cn",
    "prod": "http://cert-service.tsign.cn"
}

# 默认请求头
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "1111564052",
    "X-Tsign-Service-Group": "DEFAULT"
}


def make_http_request(url: str, data: Optional[Dict[str, Any]] = None,
                     headers: Optional[Dict[str, str]] = None,
                     method: str = "POST", timeout: int = 30) -> Dict[str, Any]:
    """
    通用HTTP请求方法
    
    :param url: 请求URL
    :param data: 请求数据
    :param headers: 请求头
    :param method: 请求方法
    :param timeout: 超时时间
    :return: 响应结果
    """
    try:
        # 合并默认请求头
        request_headers = DEFAULT_HEADERS.copy()
        if headers:
            request_headers.update(headers)

        if method.upper() == "POST":
            response = requests.post(url, json=data, headers=request_headers, timeout=timeout)
        elif method.upper() == "GET":
            response = requests.get(url, params=data, headers=request_headers, timeout=timeout)
        else:
            raise ValueError(f"不支持的请求方法: {method}")

        # 检查响应状态
        response.raise_for_status()

        # 写入请求记录
        log_data = {
            "url": url,
            "method": method,
            "headers": request_headers,
            "input": data,
            "status_code": response.status_code,
            "output": response.text
        }
        with open("../请求记录.txt", "a", encoding="utf-8") as f:
            f.write(str(log_data))
            f.write("\n")

        return response.json()

    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP请求失败: {str(e)}")
        return {
            "status": "error",
            "message": f"请求失败: {str(e)}"
        }



def create_certificate(cert_name: str, phone: str, idcard: str,
                      algorithm: str = "SM2", cert_time: str = "ONEYEAR",
                      app_id: str = "7876611670") -> Dict[str, Any]:
    """
    创建数字证书
    """
    try:
        url = f"{CERT_ENV_URLS['test']}/openca/rest/cert/createnew"
        data = {
            "certParam": {
                "algorithm": algorithm,
                "certPolicy": "COMMON",
                "certTime": cert_time,
                "certType": "SINGLE",
                "configId": app_id,
                "isUkey": False,
                "issuer": "ZHCA"
            },
            "commonParam": {
                "address": "天堂软件园",
                "mobile": phone,
                "phone": phone
            },
            "userParam": {
                "certName": cert_name,
                "licenseNumber": idcard,
                "licenseType": 19
            }
        }

        result = make_http_request(url, data)

        if result.get("status") == "error":
            return {
                 "实际请求地址":url,"实际响应":result,
                #实际请求入参
                "实际请求入参":data,
                "message": f"创建证书失败: {result}"
            }

        else:
            return {
                "status": "success",
                "data": {
                    "certId": result["certInfoId"],
                    "certName": cert_name
                },
                # 实际请求地址
                "实际请求地址": url, "实际响应": result,
                # 实际请求入参
                "实际请求入参": data,
                "message": "创建证书成功"
            }
    except Exception as e:
        logger.error(f"创建证书异常: {str(e)}")
        return {
            "status": "error", "实际请求地址": url,
            # 实际请求入参
            "实际请求入参": data,
            "message": f"创建证书异常: {str(e)}"
        }

