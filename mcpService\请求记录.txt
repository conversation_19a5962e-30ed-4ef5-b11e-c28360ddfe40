{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "non-standard-v3",
    "filter-result": "false",
    "x-tsign-gray-tag": "non-standard-v3"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 0,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435002,\"message\":\"参数错误: certNo不能为空\",\"data\":null}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "new-sign-iteration",
    "filter-result": "false",
    "x-tsign-gray-tag": "new-sign-iteration"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 0,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435002,\"message\":\"参数错误: 签署主体证书不存在,67b1cc9ed0ab4e3fb3629f0c95e285c5\",\"data\":null}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "new-sign-iteration",
    "filter-result": "false",
    "x-tsign-gray-tag": "new-sign-iteration"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "signerAccountId": "90fc72de45174e66b70653c7ec1f7aae",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 0,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 2,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435002,\"message\":\"参数错误: 签署人不能为机构类型\",\"data\":null}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "new-sign-iteration",
    "filter-result": "false",
    "x-tsign-gray-tag": "new-sign-iteration"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "90fc72de45174e66b70653c7ec1f7aae",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 0,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 2,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435011,\"message\":\"查询用户证书失败\",\"data\":null}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "new-sign-iteration",
    "filter-result": "false",
    "x-tsign-gray-tag": "new-sign-iteration"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "90fc72de45174e66b70653c7ec1f7aae",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 2,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435011,\"message\":\"查询用户证书失败\",\"data\":null}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "new-sign-iteration",
    "filter-result": "false",
    "x-tsign-gray-tag": "new-sign-iteration"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 0,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435002,\"message\":\"参数错误: 自动签署必须指定签署类型\",\"data\":null}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "new-sign-iteration",
    "filter-result": "false",
    "x-tsign-gray-tag": "new-sign-iteration"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "0",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 0,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435002,\"message\":\"参数错误: 自动签署必须指定签署类型\",\"data\":null}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "new-sign-iteration",
    "filter-result": "false",
    "x-tsign-gray-tag": "new-sign-iteration"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "0",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":0,\"message\":\"成功\",\"data\":{\"flowId\":\"703ebbe180f442d8b3b269baf63c5741\"}}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "non-standard-v3",
    "filter-result": "false",
    "x-tsign-gray-tag": "non-standard-v3"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "0",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":0,\"message\":\"成功\",\"data\":{\"flowId\":\"1c10df59b16c422e8aeba8d036ccf6a1\"}}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "non-standard-v3",
    "filter-result": "false",
    "x-tsign-gray-tag": "non-standard-v3"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "0",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":0,\"message\":\"成功\",\"data\":{\"flowId\":\"868fca85127a46a395fb6c64af393015\"}}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "non-standard-v3",
    "filter-result": "false",
    "x-tsign-gray-tag": "non-standard-v3"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "0",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":0,\"message\":\"成功\",\"data\":{\"flowId\":\"db30894047bc40feb2fd07ab3f9e0ddf\"}}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "DEFAULT",
    "filter-result": "false"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "67b1cc9ed0ab4e3fb3629f0c95e285c5",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "0",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":0,\"message\":\"成功\",\"data\":{\"flowId\":\"763d5be30e26464189cd06b5dcd61e30\"}}",
  "timestamp": null
}
================================================================================
{
  "url": "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "**********",
    "X-Tsign-Service-Group": "DEFAULT",
    "filter-result": "false"
  },
  "input": {
    "docs": [
      {
        "fileId": "db7dfff6a3a349a58ad217a992bad54e",
        "fileName": "xiugaihou.pdf"
      }
    ],
    "flowInfo": {
      "autoArchive": true,
      "autoInitiate": true,
      "businessScene": "签署"
    },
    "signers": [
      {
        "platformSign": false,
        "signOrder": 1,
        "signerAccount": {
          "authorizedAccountId": "bb4364e663ad43ee9381e65232b5ae1f",
          "signerAccountId": "f38ba6fea9084688b5db518a4ce8cb46",
          "noticeType": ""
        },
        "signfields": [
          {
            "fieldType": 0,
            "assignedPosbean": false,
            "autoExecute": true,
            "actorIndentityType": 2,
            "sealId": "",
            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
            "posBean": {
              "posPage": "1",
              "posX": 200,
              "posY": 200
            },
            "sealType": "0",
            "signDateBeanType": "1",
            "signDateBean": {
              "fontSize": 20,
              "posX": 150,
              "posY": 150
            },
            "signType": 1,
            "width": 150
          }
        ],
        "thirdOrderNo": "11111"
      }
    ]
  },
  "status_code": 200,
  "output": "{\"code\":1435002,\"message\":\"参数错误: 用户未授权或授权已过期，不允许静默签署。accountId:f38ba6fea9084688b5db518a4ce8cb46\",\"data\":null}",
  "timestamp": null
}
================================================================================
