#!/usr/bin/env python3
"""
证书管理服务 - 重构版本
使用统一的工具模块
"""
import logging
from typing import Dict, Any
from .utils import (
    make_http_request, get_test_environment_url, format_response,
    validate_required_params, get_headers
)

# 配置日志
logger = logging.getLogger(__name__)


# 使用utils模块中的统一HTTP请求方法


def get_test_account() -> Dict[str, Any]:
    """
    获取测试账号信息
    """
    try:
        url = f"{get_test_environment_url()}/random/get"
        logger.info(f"正在请求测试账号: {url}")

        result = make_http_request(url, {})
        logger.info(f"API响应: {result}")

        if result.get("status") == "error":
            return result

        # 处理新的API响应格式
        if result.get("success") and result.get("accountList"):
            account = result["accountList"][0]
            return {
                "status": "success",
                "data": {
                    "phone": account.get("phone"),
                    "idcard": account.get("idNo"),  # 注意字段名是 idNo
                    "name": account.get("name"),
                    "idcardType": "IDCARD",  # 默认值
                    "orgCode": account.get("orgCode"),
                    "englishName": account.get("englishName"),
                    "bankCard": account.get("bankCard"),
                    "orgName": account.get("orgName")
                },
                "实际请求地址": url,
                # 实际请求入参
                "实际请求入参": "",
                "message": "获取测试账号成功"
            }
        # 兼容旧的API响应格式
        elif result.get("code") == 0 and result.get("content", {}).get("accountList"):
            account = result["content"]["accountList"][0]
            return {
                "status": "success",
                "data": {
                    "phone": account.get("phone"),
                    "idcard": account.get("idcard"),
                    "name": account.get("name"),
                    "idcardType": account.get("idcardType", "IDCARD")
                },
                "message": "获取测试账号成功"
            }
        else:
            # 提供更详细的错误信息
            error_msg = result.get('message', '未知错误')
            logger.error(f"API返回错误: success={result.get('success')}, message={error_msg}")
            return {
                "status": "error",
                "message": f"获取测试账号失败: {error_msg}",
                "debug_info": {
                    "api_response": result,
                    "url": url
                }
            }

    except Exception as e:
        logger.error(f"获取测试账号异常: {str(e)}")
        return {
            "status": "error",
            "message": f"获取测试账号异常: {str(e)}",
            "debug_info": {
                "exception": str(e),
                "url": f"{ENV_URLS['test']}/random/get"
            }
        }


def test_api_connection() -> Dict[str, Any]:
    """
    测试API连接
    """
    try:
        # 测试基础连接
        url = f"{ENV_URLS['test']}/health"  # 假设有健康检查端点
        result = make_http_request(url, {}, method="GET")
        return {
            "status": "success",
            "message": "API连接正常",
            "data": result
        }
    except Exception as e:
        logger.error(f"API连接测试失败: {str(e)}")
        return {
            "status": "error",
            "message": f"API连接失败: {str(e)}",
            "debug_info": {
                "test_url": f"{ENV_URLS['test']}/health",
                "env_urls": ENV_URLS
            }
        }


def create_certificate(cert_name: str, phone: str, idcard: str,
                      algorithm: str = "SM2", cert_time: str = "ONEYEAR",
                      app_id: str = "**********") -> Dict[str, Any]:
    """
    创建数字证书
    """
    try:
        # 参数验证
        validation_error = validate_required_params(
            {"cert_name": cert_name, "phone": phone, "idcard": idcard},
            ["cert_name", "phone", "idcard"]
        )
        if validation_error:
            return format_response("error", message=validation_error)

        url = f"{get_test_environment_url('cert')}/openca/rest/cert/createnew"
        data = {
            "certParam": {
                "algorithm": algorithm,
                "certPolicy": "COMMON",
                "certTime": cert_time,
                "certType": "SINGLE",
                "configId": app_id,
                "isUkey": False,
                "issuer": "ZHCA"
            },
            "commonParam": {
                "address": "天堂软件园",
                "mobile": phone,
                "phone": phone
            },
            "userParam": {
                "certName": cert_name,
                "licenseNumber": idcard,
                "licenseType": 19
            }
        }

        result = make_http_request(url, data)

        if result.get("status") == "error":
            return format_response(
                status="error",
                message=f"创建证书失败: {result}",
                extra_info={
                    "实际请求地址": url,
                    "实际响应": result,
                    "实际请求入参": data
                }
            )

        if 1 :
            return {
                "status": "success",
                "data": {
                    "certId": result["certInfoId"],
                    "certName": cert_name
                },
                #实际请求地址
                "实际请求地址":url,"实际响应":result,
                #实际请求入参
                "实际请求入参":data,
                "message": "创建证书成功"
            }
        else:
            return {
                 "实际请求地址":url,"实际响应":result,
                #实际请求入参
                "实际请求入参":data,
                "message": f"创建证书失败: {result.get('message', '未知错误')}"
            }

    except Exception as e:
        logger.error(f"创建证书异常: {str(e)}")
        return {
              "status": "error","实际请求地址":url,
                #实际请求入参
                "实际请求入参":data,
            "message": f"创建证书异常: {str(e)}"
        }


def query_certificate_detail(cert_id: str, app_id: str = "**********") -> Dict[str, Any]:
    """
    查询证书详情
    """
    try:
        url = f"{CERT_ENV_URLS['test']}/openca/rest/cert/detail"
        data = {
            "certInfoId": cert_id,
            "configId": app_id
        }

        result = make_http_request(url, data)

        if result.get("status") == "error":
            return result

        if result.get("code") == 0 and result.get("content"):
            content = result["content"]
            return {
                "status": "success",
                "data": {
                    "certId": content.get("certInfoId"),
                    "certName": content.get("certName"),
                    "certStatus": content.get("certStatus"),
                    "createTime": content.get("createTime"),
                    "algorithm": content.get("algorithm")
                },
                # 实际请求地址
                "实际请求地址": url,
                # 实际请求入参
                "实际请求入参": data,
                "message": "查询证书详情成功"
            }
        else:
            return {
                  "status": "error","实际请求地址":url,
                #实际请求入参
                "实际请求入参":data,
                "message": f"查询证书详情失败: {result.get('message', '未知错误')}"
            }

    except Exception as e:
        logger.error(f"查询证书详情异常: {str(e)}")
        return {
              "status": "error","实际请求地址":url,
                #实际请求入参
                "实际请求入参":data,
            "message": f"查询证书详情异常: {str(e)}"
        }


def get_user_info(user_id: str) -> Dict[str, Any]:
    """
    获取用户信息
    """
    try:
        url = f"{ENV_URLS['test']}/user/info"
        data = {"userId": user_id}
        
        result = make_http_request(url, data)
        
        if result.get("status") == "error":
            return result
            
        if result.get("code") == 0:
            return {
                "status": "success",
                "data": result.get("content", {}),
                 # 实际请求地址
                "实际请求地址": url,
                # 实际请求入参
                "实际请求入参": data,
                "message": "查询证书详情成功"
            }
        else:
            return {
                  "status": "error","实际请求地址":url,
                #实际请求入参
                "实际请求入参":data,
                "message": f"获取用户信息失败: {result.get('message', '未知错误')}"
            }
            
    except Exception as e:
        logger.error(f"获取用户信息异常: {str(e)}")
        return {
              "status": "error","实际请求地址":url,
                #实际请求入参
                "实际请求入参":data,
            "message": f"获取用户信息异常: {str(e)}"
        }


def revoke_certificate(cert_info_id: str) -> Dict[str, Any]:
    """
    吊销数字证书
    """
    try:
        url = f"{CERT_ENV_URLS['test']}/openca/rest/cert/revoke"
        data = {
            "certInfoId": cert_info_id
        }
        result = make_http_request(url, data)
        if result.get("status") == "error":
            return result
        if 1:
            return {
                "status": "success",
                 # 实际请求地址
                "实际请求地址": url,
                # 实际请求入参
                "实际请求入参": data,
                "message": "查询证书详情成功",
                "data": result.get("content", {})
            }
    except Exception as e:
        logger.error(f"吊销证书异常: {str(e)}")
        return {
              "status": "error","实际请求地址":url,
                #实际请求入参
                "实际请求入参":data,
            "message": f"吊销证书异常: {str(e)}"
        }


def update_certificate(cert_info_id: str, cert_name: str, mobile: str, license_number: str, algorithm: str = "SM2", cert_time: str = "ONEYEAR", config_id: str = "**********") -> Dict[str, Any]:
    """
    更新数字证书
    """
    try:
        url = f"{CERT_ENV_URLS['test']}/openca/rest/cert/update"
        data = {
            "certInfoId": cert_info_id,
            "certParam": {
                "algorithm": algorithm,
                "certPolicy": "COMMON",
                "certTime": cert_time,
                "certType": "SINGLE",
                "configId": config_id,
                "isUkey": False,
                "issuer": "ZHCA"
            },
            "commonParam": {
                "address": "天堂软件园",
                "mobile": mobile,
                "phone": mobile
            },
            "userParam": {
                "certName": cert_name,
                "licenseNumber": license_number,
                "licenseType": 19
            }
        }
        result = make_http_request(url, data)
        if result.get("status") == "error":
            return result
        if 1:
            return {
                "status": "success",
                "message": "更新证书成功",
                # 实际请求地址
                "实际请求地址": url,
                # 实际请求入参
                "实际请求入参": data,
                "data": result.get("content", {})
            }

    except Exception as e:
        logger.error(f"更新证书异常: {str(e)}")
        return {
              "status": "error","实际请求地址":url,
                #实际请求入参
                "实际请求入参":data,
            "message": f"更新证书异常: {str(e)}"
        }
