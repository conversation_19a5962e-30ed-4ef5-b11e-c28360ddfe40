#!/usr/bin/env python3
"""
测试运行脚本 - 验证优化效果
"""
import sys
import os
import unittest
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def run_unit_tests():
    """运行单元测试"""
    logger.info("🧪 开始运行单元测试...")
    
    # 发现并运行测试
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('tests', pattern='test_*.py')
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    if result.wasSuccessful():
        logger.info("✅ 所有单元测试通过!")
        return True
    else:
        logger.error("❌ 单元测试失败!")
        return False


def validate_imports():
    """验证模块导入"""
    logger.info("📦 验证模块导入...")
    
    try:
        # 测试新的工具模块
        from mcpService.utils import (
            make_http_request, format_response, validate_required_params,
            get_test_environment_url, get_headers
        )
        logger.info("✅ utils模块导入成功")
        
        # 测试配置模块
        from app.core.config import settings
        logger.info("✅ 配置模块导入成功")
        
        # 测试重构后的服务模块
        from mcpService.certService import get_test_account, create_certificate
        logger.info("✅ 证书服务模块导入成功")
        
        # 测试新的业务模板
        from mcpService.business_template import create_certificate_template
        logger.info("✅ 业务模板模块导入成功")
        
        # 测试非标准服务
        from mcpService.non_standard_service import (
            initiate_silent_signing_flow, get_silent_signing_auth_url
        )
        logger.info("✅ 非标准服务模块导入成功")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 模块导入失败: {e}")
        return False


def validate_configuration():
    """验证配置管理"""
    logger.info("⚙️ 验证配置管理...")
    
    try:
        from app.core.config import settings
        
        # 检查基本配置
        assert settings.PROJECT_NAME == "esign-qa-mcp-service"
        assert settings.PORT == 8000
        assert hasattr(settings, 'api_urls')
        
        # 检查API URLs
        api_urls = settings.api_urls
        assert 'api' in api_urls
        assert 'cert' in api_urls
        assert 'footstone' in api_urls
        
        logger.info("✅ 配置管理验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置验证失败: {e}")
        return False


def validate_utils_functions():
    """验证工具函数"""
    logger.info("🔧 验证工具函数...")
    
    try:
        from mcpService.utils import (
            format_response, validate_required_params, get_test_environment_url
        )
        
        # 测试响应格式化
        response = format_response("success", {"test": "data"}, "测试成功")
        assert response["status"] == "success"
        assert response["data"]["test"] == "data"
        assert response["message"] == "测试成功"
        
        # 测试参数验证
        error = validate_required_params({"name": "test"}, ["name", "age"])
        assert error is not None
        assert "age" in error
        
        # 测试环境URL
        api_url = get_test_environment_url()
        assert api_url == "http://sdk.testk8s.tsign.cn"
        
        cert_url = get_test_environment_url("cert")
        assert cert_url == "http://cert-service.testk8s.tsign.cn"
        
        logger.info("✅ 工具函数验证通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 工具函数验证失败: {e}")
        return False


def check_file_structure():
    """检查文件结构"""
    logger.info("📁 检查文件结构...")
    
    required_files = [
        "mcpService/utils.py",
        "mcpService/business_template.py", 
        "mcpService/non_standard_service.py",
        "requirements_fixed.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.error(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        logger.info("✅ 文件结构检查通过")
        return True


def main():
    """主函数"""
    logger.info("🚀 开始验证项目优化效果...")
    
    results = []
    
    # 1. 检查文件结构
    results.append(check_file_structure())
    
    # 2. 验证模块导入
    results.append(validate_imports())
    
    # 3. 验证配置管理
    results.append(validate_configuration())
    
    # 4. 验证工具函数
    results.append(validate_utils_functions())
    
    # 5. 运行单元测试
    results.append(run_unit_tests())
    
    # 总结结果
    passed = sum(results)
    total = len(results)
    
    logger.info(f"\n📊 验证结果: {passed}/{total} 项通过")
    
    if passed == total:
        logger.info("🎉 所有验证项目都通过了！优化成功！")
        logger.info("\n✨ 项目优化完成，主要改进包括:")
        logger.info("   - ✅ 修复了requirements.txt编码问题")
        logger.info("   - ✅ 创建了统一的工具模块，消除代码重复")
        logger.info("   - ✅ 重构了证书服务，使用统一工具")
        logger.info("   - ✅ 优化了配置管理，支持环境切换")
        logger.info("   - ✅ 规范化了文件命名")
        logger.info("   - ✅ 添加了单元测试框架")
        logger.info("   - ✅ 生成了完整的项目文档")
        return True
    else:
        logger.error("❌ 部分验证失败，请检查相关问题")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
