import json

aa = {'action': 'UPDATE', 'dbName': 'privilege', 'tableName': 'rule_grant', 'rowKey': 'id', 'after': {'effective_time': '2025-06-19 00:00:00', 'granted_operator_oid': '', 'granted_oid': 'c576f59a35974619bef47fb66198bbb6', 'modify_time': '2025-06-19 20:53:36', 'granted_object_operator': '', 'granter_gid': '1483829966a3428db31618f595c44942', 'rule_grant_id': '22030d24-21bd-495c-8a7b-0bef53a0934b', 'expire_reason': 'NOT_EXPIRE', 'granter_oid': '40a63bb05c53406bb7943e67d707a6f4', 'granted_object': '578dfcfa4e194976bc953bd1da2dc8a8', 'grant_type': '1', 'flow_id': 'ffc1a385456e4f968e3aa96837eded5b', 'id': '3952734344661440061', 'granted_operator_gid': '', 'app_id': '5111567488', 'granted_user_role': '', 'create_time': '2025-06-19 20:48:46', 'owner_gid': 'd3acd9c6dce6449da8b8a69643bf49b6', 'rule_template_key': 'SEAL_AUTH', 'level': '2000', 'resource_type': '0', 'expire_time': '1765814399000', 'privilege_owner': '28aefefd6590451882d332a079bcb2e8', 'owner_oid': 'a80f74451d904bebb5727be55db15aab', 'notify_url': '', 'deleted': '0', 'flow_type': '1', 'apply_oid': '40a63bb05c53406bb7943e67d707a6f4', 'group_id': '01ae2368ad8a44fcbc93b569f998970e', 'resource_id': '9069ef7b-4604-4f4c-87a4-b549f8f473a9', 'apply_gid': '1483829966a3428db31618f595c44942', 'granter': 'e6158889fc994860a43900b807a4c9c2', 'granted_gid': '006a769333cc48699399ba70e87786b4', 'status': '1'}, 'content': {'effective_time': '2025-06-19 00:00:00', 'granted_operator_oid': '', 'granted_oid': 'c576f59a35974619bef47fb66198bbb6', 'modify_time': '2025-06-19 20:53:36', 'granted_object_operator': '', 'granter_gid': '1483829966a3428db31618f595c44942', 'rule_grant_id': '22030d24-21bd-495c-8a7b-0bef53a0934b', 'expire_reason': 'NOT_EXPIRE', 'granter_oid': '40a63bb05c53406bb7943e67d707a6f4', 'granted_object': '578dfcfa4e194976bc953bd1da2dc8a8', 'grant_type': '1', 'flow_id': 'ffc1a385456e4f968e3aa96837eded5b', 'id': '3952734344661440061', 'granted_operator_gid': '', 'app_id': '5111567488', 'granted_user_role': '', 'create_time': '2025-06-19 20:48:46', 'owner_gid': 'd3acd9c6dce6449da8b8a69643bf49b6', 'rule_template_key': 'SEAL_AUTH', 'level': '2000', 'resource_type': '0', 'expire_time': '1765814399000', 'privilege_owner': '28aefefd6590451882d332a079bcb2e8', 'owner_oid': 'a80f74451d904bebb5727be55db15aab', 'notify_url': '', 'deleted': '0', 'flow_type': '1', 'apply_oid': '40a63bb05c53406bb7943e67d707a6f4', 'group_id': '01ae2368ad8a44fcbc93b569f998970e', 'resource_id': '9069ef7b-4604-4f4c-87a4-b549f8f473a9', 'apply_gid': '1483829966a3428db31618f595c44942', 'granter': 'e6158889fc994860a43900b807a4c9c2', 'granted_gid': '006a769333cc48699399ba70e87786b4', 'status': '1'}, 'before': {'effective_time': '2025-06-19 00:00:00', 'granted_operator_oid': '', 'granted_oid': 'c576f59a35974619bef47fb66198bbb6', 'modify_time': '2025-06-19 20:48:52', 'granted_object_operator': '', 'granter_gid': '1483829966a3428db31618f595c44942', 'rule_grant_id': '22030d24-21bd-495c-8a7b-0bef53a0934b', 'expire_reason': 'NOT_SIGN', 'granter_oid': '40a63bb05c53406bb7943e67d707a6f4', 'granted_object': '578dfcfa4e194976bc953bd1da2dc8a8', 'grant_type': '1', 'flow_id': 'ffc1a385456e4f968e3aa96837eded5b', 'id': '3952734344661440061', 'granted_operator_gid': '', 'app_id': '5111567488', 'granted_user_role': '', 'create_time': '2025-06-19 20:48:46', 'owner_gid': 'd3acd9c6dce6449da8b8a69643bf49b6', 'rule_template_key': 'SEAL_AUTH', 'level': '2000', 'resource_type': '0', 'expire_time': '1765814399000', 'privilege_owner': '28aefefd6590451882d332a079bcb2e8', 'owner_oid': 'a80f74451d904bebb5727be55db15aab', 'notify_url': '', 'deleted': '0', 'flow_type': '1', 'apply_oid': '40a63bb05c53406bb7943e67d707a6f4', 'group_id': '01ae2368ad8a44fcbc93b569f998970e', 'resource_id': '9069ef7b-4604-4f4c-87a4-b549f8f473a9', 'apply_gid': '1483829966a3428db31618f595c44942', 'granter': 'e6158889fc994860a43900b807a4c9c2', 'granted_gid': '006a769333cc48699399ba70e87786b4', 'status': '0'}, 'gtId': '85db62b7-9cd0-11eb-ac15-7cd30ab8aa0a:1-1332161860,1fd557e8-3d80-11e8-ae8e-7cd30ab8aa6a:1-894259372,00c6c256-f32a-11ec-a47c-506b4bfee8ec:1-746459510,729391f7-8eb6-11ed-9acb-b8599f3eb9e2:1-948236091,d0151f5f-3f80-11ee-9b9d-08c0eb385804:1-4361280086', 'originalSql': ''}
print(json.dumps(aa))