#!/usr/bin/env python3
"""
通用工具类
"""
import json

import requests
import logging
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 环境配置
ENV_URLS = {
    "test": "http://sdk.testk8s.tsign.cn",
    "footstone_user_url": "http://in-test-openapi.tsign.cn"
}

# 默认请求头
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "1111564052",
    "X-Tsign-Service-Group": "DEFAULT"
}
x_group = None


def get_headers(app_id):
    headers = {"Content-Type": "application/json", "X-Tsign-Open-App-Id": app_id, "X-Tsign-Open-Auth-Mode": "simple",
               "X-Tsign-Service-Group": x_group, "filter-result": "false"}
    return headers


def make_http_request(url: str, data: Optional[Dict[str, Any]] = None,
                      headers: Optional[Dict[str, str]] = None,
                      method: str = "POST", timeout: int = 30) -> Dict[str, Any]:
    """
    通用HTTP请求方法

    :param url: 请求URL
    :param data: 请求数据
    :param headers: 请求头
    :param method: 请求方法
    :param timeout: 超时时间
    :return: 响应结果
    """
    try:
        # 合并默认请求头
        request_headers = DEFAULT_HEADERS.copy()
        if headers:
            request_headers.update(headers)

        if method.upper() == "POST":
            response = requests.post(url, json=data, headers=request_headers, timeout=timeout)
        elif method.upper() == "GET":
            response = requests.get(url, params=data, headers=request_headers, timeout=timeout)
        else:
            raise ValueError(f"不支持的请求方法: {method}")

        # 检查响应状态
        response.raise_for_status()

        # 写入请求记录
        log_data = {
            "url": url,
            "method": method,
            "headers": request_headers,
            "input": data,
            "status_code": response.status_code,
            "output": response.text
        }
        with open("../请求记录.txt", "a", encoding="utf-8") as f:
            f.write(str(log_data))
            f.write("\n")

        return response.json()

    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP请求失败: {str(e)}")
        return {
            "status": "error",
            "message": f"请求失败: {str(e)}"
        }


def 发起静默签署流程(oid):
    """
    发起静默签署流程
    """
    try:
        url = "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep"
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-App-Id": "**********",
            "X-Tsign-Open-Auth-Mode": "simple",
            "X-Tsign-Service-Group": "non-standard-v3",
            "x-tsign-gray-tag": "non-standard-v3",
            "filter-result": "false"
        }

        data = {
            "docs": [
                {
                    "fileId": "db7dfff6a3a349a58ad217a992bad54e",
                    "fileName": "xiugaihou.pdf"
                }
            ],
            "flowInfo": {
                "autoArchive": True,
                "autoInitiate": True,
                "businessScene": "签署"
            },
            "signers": [
                {
                    "platformSign": False,
                    "signOrder": 1,
                    "signerAccount": {
                        "authorizedAccountId": oid,
                        "signerAccountId": oid,
                        "noticeType": ""
                    },
                    "signfields": [
                        {
                            "fieldType": 0,
                            "assignedPosbean": False,
                            "autoExecute": True,
                            "actorIndentityType": 0,
                            "sealId": "",
                            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
                            "posBean": {
                                "posPage": "1",
                                "posX": 200,
                                "posY": 200
                            },
                            "sealType": "",
                            "signDateBeanType": "1",
                            "signDateBean": {
                                "fontSize": 20,
                                "posX": 150,
                                "posY": 150
                            },
                            "signType": 1,
                            "width": 150
                        }
                    ],
                    "thirdOrderNo": "11111"
                }
            ]
        }

        result = make_http_request(url, data, headers=headers)
        print(result)
        return result


    except Exception as e:
        logger.error(f"发起静默签署流程异常: {str(e)}")
        return {
            "status": "error",
            "实际请求地址": url,
            "实际请求入参": data,
            "message": f"发起静默签署流程异常: {str(e)}"
        }


def 获取静默签署授权链接():
    """
    获取静默签署授权链接
    """
    try:
        url = "http://in-testopenapi.tsign.cn/v1/signAuthApi/authUrl"
        headers = {
            "Content-Type": "application/json",
            "X-Tsign-Open-App-Id": "**********",
            "X-Tsign-Open-Auth-Mode": "simple",
            "X-Tsign-Service-Group": "non-standard-v3"
        }
        
        data = {
            "appId": "**********",
            "accountId": "de077f137b3d4d48a3b00e5af8dabfe2",
            "clientType": "PC",
            "language": "en-US",
            "redirectUrl": "https://www.esign.cn/",
            "notifyUrl": "http://libaohui.com.cn/callback/ding",
            "cardNo": "46902219570808919X",
            "cardType": "IDCARD"
        }

        result = make_http_request(url, data, headers=headers)
        

        if result.get("code") == 0:
            return {
                "status": "success",
                "data": {
                    "authUrl": result["data"]["authUrl"]
                },
                "实际请求地址": url,
                "实际请求入参": data,
                "message": "获取静默签署授权链接成功"
            }
        else:
            return {
                "status": "error",
                "实际请求地址": url,
                "实际请求入参": data,
                "message": f"获取静默签署授权链接失败: {result.get('message', '未知错误')}"
            }

    except Exception as e:
        logger.error(f"获取静默签署授权链接异常: {str(e)}")
        return {
            "status": "error",
            "实际请求地址": url,
            "实际请求入参": data,
            "message": f"获取静默签署授权链接异常: {str(e)}"
        }


if __name__ == '__main__':
    # res = 获取静默签署授权链接()
    # print(res)
    res = 发起静默签署流程("824c4236a3f242449b93a1c93b10ac9a")
    print(res)