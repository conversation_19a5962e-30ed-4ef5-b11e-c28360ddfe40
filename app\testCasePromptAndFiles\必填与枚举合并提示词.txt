你是一个测试专家，擅长使用httprunner写集测测试用例，请帮我设计测试用例：
1. 参数定义规则：
   1. **必填参数识别**
   - 明确区分普通必填和条件必填参数
   - 条件必填参数需创建触发其必填条件的场景用例
   - 验证必填参数时需保证其他必填参数完整

2. **参数范围控制**
   - 仅使用接口文档明确列出的参数
   - 禁止添加文档未说明的参数
   - 非必填参数不做必填校验

3. **嵌套参数处理**
   - 递归处理所有层级的参数
   - 特别注意嵌套参数的必填性
   - 父子级参数需同步验证

4. **枚举参数提取**
   - 数值范围型（如："操作方式(1-3)"）
   - 离散枚举值（如："操作方式(1,2,3)"）
   - 带说明的枚举（如："1-你,2-我,3-他"）
   - 字符串枚举（如："ni,wo,ta"）

2. 测试用例设计原则：
   a) 每个用例仅测试一个参数的缺失场景。
   b) 其他必填参数必须正确提供。
   c) 仅包含必要的参数，非必填参数可以省略。
   d) 同类参数在不同用例中保持一致的值。
   e) 【强制】每个枚举参数的每个可选枚举值都必须生成一个独立的完整正向用例，不允许合并或省略。例如：
	   - 如果参数A有值1,2,3，则必须生成3个独立的用例，分别使用值1,2,3
	   - 不允许在一个用例中测试多个枚举值
	   - 不允许只测试部分枚举值
	   - 每个枚举值的用例必须包含所有必要的参数
   f) 每个枚举参数都需覆盖非法值的反向用例（如：超出范围、类型错误、格式不符等）。

3. 用例格式要求：
   参考用例格式.yml，不允许分开写。

4. 测试数据规范：
   a) 使用有意义的测试数据。
   b) 枚举值使用实际可用的值。
   c) 错误提示统一使用"参数错误: "作为前缀。
   d) 条件类错误需说明触发条件。
   e) 非法值用明显错误的数据（如：不存在的枚举、类型错误、格式错误等）。

5. 用例覆盖范围：
   a) 必须包含一个正向用例作为基准。
   b) 覆盖所有基础必填参数缺失场景。
   c) 覆盖所有嵌套必填参数缺失场景。
   d) 覆盖所有条件必填参数缺失场景。
   e) 必须为每个枚举参数的每个可选值生成独立的完整正向用例：
   f) 覆盖所有枚举参数的非法值场景。

6. 参数提取范围：
   a) 所有请求参数（不包括响应参数）。
   b) 所有嵌套层级的参数。
   c) 所有明确列出可选值、取值范围、默认值、单位、格式要求、业务状态等的参数。
   d) 只允许提取接口文档中明文列出的参数。
   e) 确保提取所有类型的枚举参数，包括整数类型和字符串类型的枚举。
   f) 仅提取那些在接口文档中明确列出可选值的参数。

7. 特别注意：
   a) 接口说明中会说明是否是条件必填；如果是，就观察其必填条件并创建一个让其必填的场景。
   b) 验证某个必填参数是否必填时，其他必填参数不允许省略。
   c) 不允许私自臆想、假设、推理、猜测出文档中没有明确标注必填的参数。
   d) 递归地读取所有层的入参，处理好各个对应的层级关系。
   e) 对于有嵌套关系的参数，父级和子级参数都需要进行进行验证（如A在B下，则需要对A和B都做验证）。
   f) 只输出最终的测试用例，不输出枚举参数本身，不输出任何解释说明。
   g) 只提取接口文档中明确列出的参数。
   h) 不允许根据上下文或命名规律进行推测。
   i) 写用例时的入参尽量以variables的形式传入
   j) 枚举值用例完整性原则：
	   - 必须为每个枚举值生成独立的完整正向用例
	   - 枚举值数量 = 必须生成的独立正向用例数量
	   - 不允许合并或省略任何枚举值的用例
	   - 每个枚举值的用例必须包含所有必要的参数
	   - 违反此原则将被视为严重错误
	   - 不要错误地认为只需要覆盖一个正向场景就够了
