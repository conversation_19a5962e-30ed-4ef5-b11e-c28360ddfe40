"""
应用配置设置 - 优化版本
"""
from pydantic_settings import BaseSettings
from typing import Optional, Dict

class Settings(BaseSettings):
    """应用配置类"""

    # 项目基本信息
    PROJECT_NAME: str = "esign-qa-mcp-service"
    PROJECT_DESCRIPTION: str = "电子签名QA自动化测试MCP服务"
    VERSION: str = "1.0.0"

    # 服务配置
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    API_V1_STR: str = "/mcpController/v1"

    # MCP配置
    MCP_NAME: str = "FastMCP API Server"
    MCP_DESCRIPTION: str = "Professional MCP server with separated business logic"

    # 数据库配置
    DATABASE_URL: str = "sqlite:///./test.db"

    # 外部API配置 - 统一管理
    EXTERNAL_API_BASE_URL: str = "http://sdk.testk8s.tsign.cn"
    CERT_API_BASE_URL: str = "http://cert-service.testk8s.tsign.cn"
    FOOTSTONE_API_BASE_URL: str = "http://in-test-openapi.tsign.cn"

    # 默认应用配置
    DEFAULT_APP_ID: str = "7876611670"
    DEFAULT_CERT_APP_ID: str = "1111564052"

    # 请求配置
    REQUEST_TIMEOUT: int = 30
    LOG_REQUESTS: bool = True

    # 环境配置
    ENVIRONMENT: str = "test"  # test, prod

    @property
    def api_urls(self) -> Dict[str, str]:
        """获取API URL配置"""
        if self.ENVIRONMENT == "prod":
            return {
                "api": "http://sdk.tsign.cn",
                "cert": "http://cert-service.tsign.cn",
                "footstone": "http://openapi.tsign.cn"
            }
        else:
            return {
                "api": self.EXTERNAL_API_BASE_URL,
                "cert": self.CERT_API_BASE_URL,
                "footstone": self.FOOTSTONE_API_BASE_URL
            }

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    # AI模型配置
#     DEEPSEEK_API_KEY: str = "***********************************"
#     DEEPSEEK_BASE_URL: str = "https://api.deepseek.com"
#     DEEPSEEK_MODEL: str = "deepseek-chat"
    # 阿里百炼配置
    DEEPSEEK_API_KEY: str = "***********************************"
    DEEPSEEK_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    DEEPSEEK_MODEL: str = "qwen-turbo"

    # 日志配置
    LOG_LEVEL: str = "INFO"
    
    class Config:
        env_file = ".env"

# 创建全局配置实例
settings = Settings()
