#!/usr/bin/env python3
"""
业务模板服务 - 重构版本
原文件: 业务模板.py
"""
import logging
from typing import Dict, Any
from .utils import (
    make_http_request, get_test_environment_url, format_response,
    validate_required_params, get_headers
)

# 配置日志
logger = logging.getLogger(__name__)


def create_certificate_template(cert_name: str, phone: str, idcard: str,
                               algorithm: str = "SM2", cert_time: str = "ONEYEAR",
                               app_id: str = "7876611670") -> Dict[str, Any]:
    """
    创建数字证书 - 模板方法
    """
    try:
        # 参数验证
        validation_error = validate_required_params(
            {"cert_name": cert_name, "phone": phone, "idcard": idcard},
            ["cert_name", "phone", "idcard"]
        )
        if validation_error:
            return format_response("error", message=validation_error)

        url = f"{get_test_environment_url('cert')}/openca/rest/cert/createnew"
        data = {
            "certParam": {
                "algorithm": algorithm,
                "certPolicy": "COMMON",
                "certTime": cert_time,
                "certType": "SINGLE",
                "configId": app_id,
                "isUkey": False,
                "issuer": "ZHCA"
            },
            "commonParam": {
                "address": "天堂软件园",
                "mobile": phone,
                "phone": phone
            },
            "userParam": {
                "certName": cert_name,
                "licenseNumber": idcard,
                "licenseType": 19
            }
        }

        result = make_http_request(url, data)

        if result.get("status") == "error":
            return format_response(
                status="error",
                message=f"创建证书失败: {result}",
                extra_info={
                    "实际请求地址": url,
                    "实际响应": result,
                    "实际请求入参": data
                }
            )

        # 处理成功响应
        if result.get("code") == 0:
            cert_data = result.get("data", {})
            return format_response(
                status="success",
                data={
                    "certId": cert_data.get("certId"),
                    "certInfoId": cert_data.get("certInfoId"),
                    "serialNumber": cert_data.get("serialNumber")
                },
                message="创建证书成功",
                extra_info={
                    "实际请求地址": url,
                    "实际请求入参": data
                }
            )
        else:
            return format_response(
                status="error",
                message=f"创建证书失败: {result.get('message', '未知错误')}",
                extra_info={
                    "实际请求地址": url,
                    "实际响应": result,
                    "实际请求入参": data
                }
            )

    except Exception as e:
        logger.error(f"创建证书异常: {str(e)}")
        return format_response(
            status="error",
            message=f"创建证书异常: {str(e)}",
            extra_info={
                "debug_info": {
                    "exception": str(e),
                    "url": f"{get_test_environment_url('cert')}/openca/rest/cert/createnew"
                }
            }
        )
