#!/usr/bin/env python3
"""
非标准业务服务 - 重构版本
原文件: 非标3.py
"""
import logging
from typing import Dict, Any
# from .utils import (
#     make_http_request, get_test_environment_url, format_response,
#     validate_required_params, get_headers
# )
from mcpService.utils import *

# 配置日志
logger = logging.getLogger(__name__)


def initiate_silent_signing_flow(oid: str) -> Dict[str, Any]:
    """
    发起静默签署流程
    
    :param oid: 组织ID
    :return: 响应结果
    """
    try:
        # 参数验证
        validation_error = validate_required_params({"oid": oid}, ["oid"])
        if validation_error:
            return format_response("error", message=validation_error)

        url = "http://in-test-openapi.tsign.cn/api/v2/signflows/createFlowOneStep"
        headers = get_headers(
            app_id="**********",
            # service_group="non-standard-v3"  # new-sign-iteration   non-standard-v3
        )
        # headers.update({
        #     "x-tsign-gray-tag": "non-standard-v3"
        # })

        data = {
            "docs": [
                {
                    "fileId": "db7dfff6a3a349a58ad217a992bad54e",
                    "fileName": "xiugaihou.pdf"
                }
            ],
            "flowInfo": {
                "autoArchive": True,
                "autoInitiate": True,
                "businessScene": "签署"
            },
            "signers": [
                {
                    "platformSign": False,
                    "signOrder": 1,
                    "signerAccount": {
                        "authorizedAccountId":"bb4364e663ad43ee9381e65232b5ae1f" ,
                        "signerAccountId": oid,
                        "noticeType": ""
                    },
                    "signfields": [
                        {
                            "fieldType": 0,
                            "assignedPosbean": False,
                            "autoExecute": True,
                            "actorIndentityType": 2,
                            "sealId": "",
                            "fileId": "db7dfff6a3a349a58ad217a992bad54e",
                            "posBean": {
                                "posPage": "1",
                                "posX": 200,
                                "posY": 200
                            },
                            "sealType": "0",
                            "signDateBeanType": "1",
                            "signDateBean": {
                                "fontSize": 20,
                                "posX": 150,
                                "posY": 150
                            },
                            "signType": 1,
                            "width": 150
                        }
                    ],
                    "thirdOrderNo": "11111"
                }
            ]
        }

        result = make_http_request(url, data, headers=headers)
        
        if result.get("code") == 0:
            return format_response(
                status="success",
                data=result.get("data"),
                message="发起静默签署流程成功",
                extra_info={
                    "实际请求地址": url,
                    "实际请求入参": data
                }
            )
        else:
            return format_response(
                status="error",
                message=f"发起静默签署流程失败: {result.get('message', '未知错误')}",
                extra_info={
                    "实际请求地址": url,
                    "实际响应": result,
                    "实际请求入参": data
                }
            )

    except Exception as e:
        logger.error(f"发起静默签署流程异常: {str(e)}")
        return format_response(
            status="error",
            message=f"发起静默签署流程异常: {str(e)}",
            extra_info={
                "debug_info": {
                    "exception": str(e),
                    "url": url if 'url' in locals() else None
                }
            }
        )


def get_silent_signing_auth_url(appId="**********",accountId="de077f137b3d4d48a3b00e5af8dabfe2" ) -> Dict[str, Any]:
    """
    获取静默签署授权链接
    
    :return: 响应结果
    """
    try:
        url = "http://in-testopenapi.tsign.cn/v1/signAuthApi/authUrl"
        headers = get_headers(
            app_id=appId,
            service_group="non-standard-v3"
        )
        
        data = {
            "appId": appId,
            "accountId": accountId,
            "clientType": "PC",
            "language": "en-US",
            "redirectUrl": "https://www.esign.cn/",
            "notifyUrl": "http://libaohui.com.cn/callback/ding",
            # "cardNo": "46902219570808919X",
            # "cardType": "IDCARD"
        }

        result = make_http_request(url, data, headers=headers)
        
        if result.get("code") == 0:
            return format_response(
                status="success",
                data={
                    "authUrl": result["data"]["authUrl"]
                },
                message="获取静默签署授权链接成功",
                extra_info={
                    "实际请求地址": url,
                    "实际请求入参": data
                }
            )
        else:
            return format_response(
                status="error",
                message=f"获取静默签署授权链接失败: {result.get('message', '未知错误')}",
                extra_info={
                    "实际请求地址": url,
                    "实际响应": result,
                    "实际请求入参": data
                }
            )

    except Exception as e:
        logger.error(f"获取静默签署授权链接异常: {str(e)}")
        return format_response(
            status="error",
            message=f"获取静默签署授权链接异常: {str(e)}",
            extra_info={
                "debug_info": {
                    "exception": str(e),
                    "url": url if 'url' in locals() else None
                }
            }
        )


if __name__ == '__main__':
    # 测试函数
    # res = get_silent_signing_auth_url()
    # print(res)
    res = initiate_silent_signing_flow("67b1cc9ed0ab4e3fb3629f0c95e285c5")
    print(res)
