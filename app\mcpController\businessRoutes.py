#!/usr/bin/env python3
"""
业务路由 - 重构优化版本
整合原有的MCP业务功能，使用统一的工具函数
"""
from fastapi import APIRouter
import logging

# 导入统一的工具函数
from mcpService.certService import (
    get_test_account, create_certificate, query_certificate_detail, get_user_info,
    revoke_certificate, update_certificate
)
from mcpService.promptService import (
    get_prompt_types, get_prompt_content, search_prompts,
    get_接口生成提示词, get_必填参数提示词, get_枚举值提示词,
    get_必填与枚举合并提示词, get_通用HttpRunner测试用例生成提示词,
   get_全套集测提示词
)
from mcpService.accountService import (
    register_test_person_account,register_test_company_account
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建业务路由器
business_router = APIRouter(tags=["业务工具"])


# ============= 证书管理 =============


@business_router.post(
    "/获取测试账号",
    summary="📱 获取测试账号",
    description="获取测试账号信息，包括手机号、证件号、姓名等",
    operation_id="get_test_account"
)
async def get_test_account_endpoint():
    """
    获取测试账号信息


    返回示例:
    {
      "status": "success",
      "data": {
        "phone": "***********",        # 手机号，可用于创建证书接口的 phone 参数
        "idcard": "341321196102220130",# 身份证号，可用于创建证书接口的 idcard 参数
        "name": "测试东广政",           # 姓名，**可直接作为创建证书接口的 cert_name 参数**
        "idcardType": "IDCARD",        # 证件类型
        "orgCode": "91000000QLRGC4C61Q", # 企业证件号
        "englishName": "Clarence Chad Price", # 英文名
        "bankCard": "****************", # 银行卡号
        "orgName": "esigntest东广政经营的个体工商户" # 企业名称
      }
    }

    字段说明:
    | 字段         | 说明                         | 可用于下游接口参数         |
    |--------------|------------------------------|---------------------------|
    | phone        | 手机号                       | create_certificate.phone  |
    | idcard       | 身份证号                     | create_certificate.idcard |
    | name         | 姓名                         | create_certificate.cert_name |
    | ...          | ...                          | ...                       |

    依赖关系说明:
    - 本接口返回的 name 字段，推荐作为“创建证书”接口的 cert_name 参数使用，实现自动化数据流转。
    - 其他字段同理，可用于下游相关接口参数。
    获取测试账号信息

    @output-mapping
    | 输出字段 | 下游接口         | 下游参数名         | 说明                   |
    |----------|------------------|--------------------|------------------------|
    | name     | create_certificate | cert_name        | 账号姓名可作为证书姓名 |
    | phone    | create_certificate | phone            | 账号手机号可用         |
    | idcard   | create_certificate | idcard           | 账号身份证号可用       |

    @usage
    - 推荐将本接口返回的 name 字段，直接作为 create_certificate 接口的 cert_name 参数。
    - 其他字段同理，可自动流转到下游接口。

    @response-example
    {
      "status": "success",
      "data": {
        "name": "测试东广政",
        "phone": "***********",
        "idcard": "341321196102220130"
      }
    }

    """
    return get_test_account()


@business_router.post(
    "/创建证书",
    summary="🔐 创建证书",
    description="创建数字证书",
    operation_id="create_certificate"
)
async def create_certificate_endpoint(
    cert_name: str,#
    phone: str,
    idcard: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    app_id: str = "**********"
):
    """
    创建数字证书
    :param cert_name:  姓名
    :param phone:   手机号
    :param idcard:  身份证号
    :param algorithm:  加密方式
    :param cert_time:  证书生效市场
    :param app_id:   应用id
    :return:
    """

    return create_certificate(cert_name, phone, idcard, algorithm, cert_time, app_id)


@business_router.post(
    "/查询证书详情",
    summary="🔍 查询证书详情",
    description="查询证书详情信息",
    operation_id="query_certificate_detail"
)
async def query_certificate_detail_endpoint(
    cert_id: str,
    app_id: str = "**********"
):
    """查询证书详情"""
    return query_certificate_detail(cert_id, app_id)


@business_router.post(
    "/吊销证书",
    summary="❌ 吊销证书",
    description="吊销数字证书",
    operation_id="revoke_certificate"
)
async def revoke_certificate_endpoint(cert_info_id: str):
    """吊销数字证书"""
    return revoke_certificate(cert_info_id)


@business_router.post(
    "/更新证书",
    summary="✏️ 更新证书",
    description="更新数字证书信息",
    operation_id="update_certificate"
)
async def update_certificate_endpoint(
    cert_info_id: str,
    cert_name: str,
    mobile: str,
    license_number: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    config_id: str = "**********"
):
    """更新数字证书"""
    return update_certificate(cert_info_id, cert_name, mobile, license_number, algorithm, cert_time, config_id)


# ============= 用户管理 =============

@business_router.post(
    "/获取用户信息",
    summary="👤 获取用户信息",
    description="获取用户信息",
    operation_id="get_user_info"
)
async def get_user_info_endpoint(user_id: str):
    """获取用户信息"""
    return get_user_info(user_id)


# ============= 集测提示词管理 =============

@business_router.post(
    "/获取提示词类型",
    summary="📋 获取提示词类型",
    description="获取所有可用的提示词类型",
    operation_id="get_prompt_types"
)
async def get_prompt_types_endpoint():
    """获取所有可用的提示词类型"""
    return get_prompt_types()


@business_router.post(
    "/获取提示词内容",
    summary="📄 获取提示词内容",
    description="获取指定类型的提示词内容",
    operation_id="get_prompt_content"
)
async def get_prompt_content_endpoint(prompt_type: str):
    """获取指定类型的提示词内容"""
    return get_prompt_content(prompt_type)


@business_router.post(
    "/搜索提示词",
    summary="🔍 搜索提示词",
    description="根据关键词搜索提示词",
    operation_id="search_prompts"
)
async def search_prompts_endpoint(keyword: str):
    """根据关键词搜索提示词"""
    return search_prompts(keyword)


@business_router.post(
    "/获取接口生成提示词",
    summary="🔧 获取接口生成提示词",
    description="获取接口生成提示词",
    operation_id="get_interface_generation_prompt"
)
async def get_interface_generation_prompt_endpoint():
    """获取接口生成提示词"""
    return get_接口生成提示词()


@business_router.post(
    "/获取必填参数提示词",
    summary="📝 获取必填参数提示词",
    description="获取必填参数提示词",
    operation_id="get_required_params_prompt"
)
async def get_required_params_prompt_endpoint():
    """获取必填参数提示词"""
    return get_必填参数提示词()


@business_router.post(
    "/获取枚举值提示词",
    summary="📊 获取枚举值提示词",
    description="获取枚举值提示词",
    operation_id="get_enum_values_prompt"
)
async def get_enum_values_prompt_endpoint():
    """获取枚举值提示词"""
    return get_枚举值提示词()


@business_router.post(
    "/获取必填与枚举合并提示词",
    summary="📋 获取必填与枚举合并提示词",
    description="获取必填与枚举合并提示词",
    operation_id="get_required_enum_merged_prompt"
)
async def get_required_enum_merged_prompt_endpoint():
    """获取必填与枚举合并提示词"""
    return get_必填与枚举合并提示词()


@business_router.post(
    "/获取通用HttpRunner测试用例生成提示词",
    summary="🧪 获取通用HttpRunner测试用例生成提示词",
    description="获取通用HttpRunner测试用例生成提示词",
    operation_id="get_general_httprunner_prompt"
)
async def get_general_httprunner_prompt_endpoint():
    """获取通用HttpRunner测试用例生成提示词"""
    return get_通用HttpRunner测试用例生成提示词()


@business_router.post(
    "/获取全套集测提示词",
    summary="📚 获取全套集测提示词",
    description="获取所有集测提示词内容",
    operation_id="get_all_test_prompts"
)
async def get_all_test_prompts_endpoint():
    """获取所有集测提示词内容"""
    return get_全套集测提示词()


@business_router.post(
    "/注册测试企业账号",
    summary="在e签宝注册一个企业测试账号",
    description="在e签宝注册一个企业测试账号",
    operation_id="register_test_company_account"
)
async def register_test_company_account_endpoint(app_id, idNumber: str, mobile: str, name: str, thirdPartyUserId: str, orgLegalIdNumber: str, orgLegalName: str):
    """在e签宝注册一个企业测试账号"""
    return register_test_company_account(app_id, idNumber, mobile, name, thirdPartyUserId, orgLegalIdNumber, orgLegalName)


@business_router.post(
    "/注册测试账号",
    summary="在e签宝注册一个个人测试账号",
    description="在e签宝注册一个个人测试账号",
    operation_id="register_test_person_account_endpoint"
)
async def register_test_person_account_endpoint(app_id,idNo: str, mobile: str, name: str, thirdPartyUserId: str):
    """获取所有集测提示词内容"""
    return register_test_person_account(app_id,idNo, mobile, name, thirdPartyUserId)