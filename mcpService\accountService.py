#!/usr/bin/env python3
"""
通用工具类
"""
import json

import requests
import logging
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 环境配置
ENV_URLS = {
    "test": "http://sdk.testk8s.tsign.cn",
    "footstone_user_url": "http://in-test-openapi.tsign.cn"
}


# 默认请求头
DEFAULT_HEADERS = {
    "Content-Type": "application/json",
    "X-Tsign-Open-Auth-Mode": "simple",
    "X-Tsign-Open-App-Id": "1111564052",
    "X-Tsign-Service-Group": "DEFAULT"
}
x_group = None
def get_headers(app_id):
    headers = {"Content-Type": "application/json", "X-Tsign-Open-App-Id": app_id, "X-Tsign-Open-Auth-Mode": "simple",
               "X-Tsign-Service-Group": x_group, "filter-result": "false"}
    return headers

def make_http_request(url: str, data: Optional[Dict[str, Any]] = None,
                      headers: Optional[Dict[str, str]] = None,
                      method: str = "POST", timeout: int = 30) -> Dict[str, Any]:
    """
    通用HTTP请求方法

    :param url: 请求URL
    :param data: 请求数据
    :param headers: 请求头
    :param method: 请求方法
    :param timeout: 超时时间
    :return: 响应结果
    """
    try:
        # 合并默认请求头
        request_headers = DEFAULT_HEADERS.copy()
        if headers:
            request_headers.update(headers)

        if method.upper() == "POST":
            response = requests.post(url, json=data, headers=request_headers, timeout=timeout)
        elif method.upper() == "GET":
            response = requests.get(url, params=data, headers=request_headers, timeout=timeout)
        else:
            raise ValueError(f"不支持的请求方法: {method}")

        # 检查响应状态
        response.raise_for_status()

        # 写入请求记录
        log_data = {
            "url": url,
            "method": method,
            "headers": request_headers,
            "input": data,
            "status_code": response.status_code,
            "output": response.text
        }
        with open("../请求记录.txt", "a", encoding="utf-8") as f:
            f.write(str(log_data))
            f.write("\n")

        return response.json()

    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP请求失败: {str(e)}")
        return {
            "status": "error",
            "message": f"请求失败: {str(e)}"
        }





def register_test_person_account(app_id,idNo: str, mobile: str, name: str, thirdPartyUserId: str) -> Dict[str, Any]:
    """
    注册测试的账号， 需要传入appid，idNo


    :param idNo:身份证号
    :param mobile:电话号码
    :param name:姓名
    :param thirdPartyUserId:
    :param accountType 1为个人，2为企业
    :return:

    企业入参参考
    - test:
    name: "创建机构-泗县深泉纯净水有限公司"
    variables:
      - thirdPartyUserId: sixian2041132
      - creator: $accountId_yuzan
      - idNumber: 91341324MA2RMB1W3T
      - idType: CRED_ORG_USCC
      - name: 泗县深泉纯净水有限公司
      - orgLegalIdNumber: 331082199211223080
      - orgLegalName: 梁贤红
    api: api/iterate_cases/o_createByThirdPartyUserId.yml
    extract:
      - sixian_organize_id: content.data.orgId

    """
    try:
        url = f"{ENV_URLS['footstone_user_url']}/v1/accounts/createByThirdPartyUserId"
        logger.info(f"正在请求创建账号: {url}")

        headers = get_headers(app_id)
        payload = {
            "idNumber": idNo,
            "idType":  "CRED_PSN_CH_IDCARD",
            "mobile": mobile,
            "name": name,
            "thirdPartyUserId": thirdPartyUserId
        }
        
        result = make_http_request(url, payload, method="POST", headers=headers)
        logger.info(f"API响应: {result}")

        # 处理新的API响应格式

        return {
            "status": "success",
            "data": {
                "accountId": result.get("data").get("accountId"),
            },
            "实际请求地址": url,
            "实际请求入参": payload,
            "message": "创建测试账号成功"
        }

    except Exception as e:
        logger.error(f"创建测试账号异常: {str(e)}")
        return {
            "status": "error",
            "message": f"创建测试账号异常: {str(e)}",
            "debug_info": {
                "exception": str(e),
                "url": f"{ENV_URLS['footstone_user_url']}/v1/accounts/createByThirdPartyUserId"
            }
        }


def register_test_company_account(app_id, idNumber: str, mobile: str, name: str, thirdPartyUserId: str, orgLegalIdNumber: str, orgLegalName: str) -> Dict[str, Any]:
    """
    注册测试的企业账号，需要传入appid，idNumber

    :param idNumber: 统一社会信用代码
    :param mobile: 电话号码
    :param name: 企业名称
    :param thirdPartyUserId: 第三方用户ID
    :param orgLegalIdNumber: 企业法人身份证号
    :param orgLegalName: 企业法人姓名
    :return:
    """
    try:
        url = f"{ENV_URLS['footstone_user_url']}/v1/organizations/createByThirdPartyUserId"
        logger.info(f"正在请求创建企业账号: {url}")

        headers = get_headers(app_id)
        payload = {
            "idNumber": idNumber,
            "idType": "CRED_ORG_USCC",
            "mobile": mobile,
            "name": name,
            "thirdPartyUserId": thirdPartyUserId,
            "orgLegalIdNumber": orgLegalIdNumber,
            "orgLegalName": orgLegalName
        }

        result = make_http_request(url, payload, method="POST", headers=headers)
        logger.info(f"API响应: {result}")

        return {
            "status": "success",
            "data": {
                "orgId": result.get("data").get("orgId"),
            },
            "实际请求地址": url,
            "实际请求入参": payload,
            "message": "创建测试企业账号成功"
        }

    except Exception as e:
        logger.error(f"创建测试企业账号异常: {str(e)}")
        return {
            "status": "error",
            "message": f"创建测试企业账号异常: {str(e)}",
            "debug_info": {
                "exception": str(e),
                "url": f"{ENV_URLS['footstone_user_url']}/v1/organizations/createByThirdPartyUserId"
            }
        }
if __name__ == '__main__':
    data ={
    "app_id": "**********",
    "idNumber": "9100000094E8NCT9X5",
    "mobile": "***********",
    "name": "esigntest戴清轩经营的个体工商户",
    "orgLegalIdNumber": "371325197503139638",
    "orgLegalName": "测试戴清轩",
    "thirdPartyUserId": "test_user_456"
}
    print(register_test_company_account(**data))